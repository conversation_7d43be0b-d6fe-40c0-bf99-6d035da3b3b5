// src/services/accessService/storeAssignmentService.ts

import knex from "../../../knex/knex";
import { PaginationInput, SortBy, Ordering } from "../../generated/graphql";

export interface AssignableStore {
  id: string;
  name: string;
  address: string;
  route_id?: string;
  route_name?: string;
  phone_number?: string;
}

export interface StoreAssignmentFilters {
  supplierId: string;
  query?: string; // Search by name or address
  routeIds?: string[]; // Filter stores by specific routes
  excludeAssigned?: boolean; // Exclude stores already assigned to employees
  employeeId?: string; // For getting stores assigned to specific employee
}

/**
 * Helper function to apply filters to a query builder
 */
const applyFilters = (query: any, filters: StoreAssignmentFilters) => {
  // Apply search filter
  if (filters.query) {
    query = query.where(function () {
      this.where("attain_user.name", "ilike", `%${filters.query}%`).orWhere(
        "attain_user.address",
        "ilike",
        `%${filters.query}%`
      );
    });
  }

  // Apply route filter
  if (filters.routeIds && filters.routeIds.length > 0) {
    query = query.whereIn("attain_user.route_id", filters.routeIds);
  }

  // Apply exclude assigned filter
  if (filters.excludeAssigned) {
    query = query.whereNotExists(function () {
      this.select(knex.raw(1))
        .from("store_assignment")
        .whereRaw("store_assignment.store_id = attain_user.id");
    });
  }

  // Apply employee filter
  if (filters.employeeId) {
    query = query.whereExists(function () {
      this.select(knex.raw(1))
        .from("store_assignment")
        .whereRaw("store_assignment.store_id = attain_user.id")
        .where("store_assignment.employee_id", filters.employeeId);
    });
  }

  return query;
};

/**
 * Get stores that can be assigned to employees, with search and filtering
 */
export const getAssignableStores = async ({
  filters,
  pagination = { offset: 0, limit: 100 },
  sortBy = { field: "name", ordering: Ordering.Asc },
}: {
  filters: StoreAssignmentFilters;
  pagination?: PaginationInput;
  sortBy?: SortBy;
}): Promise<{ stores: AssignableStore[]; totalCount: number }> => {
  // Build base query for active stores
  let baseQuery = knex
    .select(
      "attain_user.id",
      "attain_user.name",
      "attain_user.address",
      // "attain_user.route_id",
      "attain_user.phone_number"
      // "route.name as route_name"
    )
    .from("attain_user")
    // .leftJoin("route", "attain_user.route_id::integer", "route.id")
    .innerJoin("supplier_times", "attain_user.id", "supplier_times.business_id")
    .where("attain_user.archived", false)
    .where("supplier_times.supplier_id", filters.supplierId);

  // Apply filters to base query
  baseQuery = applyFilters(baseQuery, filters);

  // Create separate count query with same base conditions and filters
  let countQuery = knex("attain_user")
    .innerJoin("supplier_times", "attain_user.id", "supplier_times.business_id")
    .where("attain_user.archived", false)
    .where("supplier_times.supplier_id", filters.supplierId);

  // Apply the same filters to count query
  countQuery = applyFilters(countQuery, filters);

  // Execute count query
  const countResult = await countQuery.count("* as count").first();
  const totalCount = parseInt((countResult as any)?.count || "0", 10);

  // Apply sorting and pagination to main query
  const sortField = ["name", "address"].includes(sortBy.field)
    ? sortBy.field
    : "name";
  const paginatedQuery = baseQuery
    .orderBy(`attain_user.${sortField}`, sortBy.ordering.toLowerCase())
    .offset(pagination.offset || 0)
    .limit(pagination.limit || 100);

  const rows = await paginatedQuery;

  const stores: AssignableStore[] = rows.map((row) => ({
    id: row.id,
    name: row.name || "",
    address: row.address || "", // Handle null addresses
    // route_id: row.route_id,
    // route_name: row.route_name,
    phone_number: row.phone_number || "", // Handle null phone numbers
  }));

  return { stores, totalCount };
};

/**
 * Get all store IDs for a supplier (for "select all" functionality)
 * This only returns IDs to avoid memory issues with large datasets
 */
export const getAllStoreIds = async (
  supplierId: string,
  query?: string
): Promise<{ storeIds: string[]; totalCount: number }> => {
  console.log("getAllStoreIds called with:", { supplierId, query });

  if (!supplierId) {
    console.error("getAllStoreIds: supplierId is required");
    return { storeIds: [], totalCount: 0 };
  }

  try {
    let baseQuery = knex("attain_user")
      .select("attain_user.id")
      .innerJoin(
        "supplier_times",
        "attain_user.id",
        "supplier_times.business_id"
      )
      .where("attain_user.archived", false)
      .where("supplier_times.supplier_id", supplierId);

    // Apply search filter if provided
    if (query && query.trim().length > 0) {
      baseQuery = baseQuery.where(function () {
        this.where("attain_user.name", "ilike", `%${query.trim()}%`).orWhere(
          "attain_user.address",
          "ilike",
          `%${query.trim()}%`
        );
      });
    }

    const rows = await baseQuery;
    const storeIds = rows.map((row) => row.id);

    console.log(`getAllStoreIds found ${storeIds.length} stores`);

    return {
      storeIds,
      totalCount: storeIds.length,
    };
  } catch (error) {
    console.error("Error in getAllStoreIds:", error);
    return { storeIds: [], totalCount: 0 };
  }
};

/**
 * Get store details for specific store IDs (for displaying selected stores)
 * Limited to prevent memory issues
 */
export const getStoreDetailsByIds = async (
  storeIds: string[],
  limit: number = 50
): Promise<AssignableStore[]> => {
  if (storeIds.length === 0) return [];

  // Limit the number of store details we fetch to prevent memory issues
  const limitedIds = storeIds.slice(0, limit);

  const rows = await knex
    .select(
      "attain_user.id",
      "attain_user.name",
      "attain_user.address",
      "attain_user.phone_number"
    )
    .from("attain_user")
    .whereIn("attain_user.id", limitedIds)
    .where("attain_user.archived", false)
    .orderBy("attain_user.name");

  return rows.map((row) => ({
    id: row.id,
    name: row.name || "",
    address: row.address || "", // Handle null addresses
    phone_number: row.phone_number || "", // Handle null phone numbers
  }));
};

/**
 * Assign multiple stores to an employee
 */
export const assignStoresToEmployee = async (
  employeeId: string,
  storeIds: string[]
): Promise<void> => {
  if (!employeeId || !storeIds.length) {
    throw new Error("Employee ID and store IDs are required");
  }

  // Remove existing assignments for this employee
  await knex("store_assignment").where({ employee_id: employeeId }).delete();

  // Handle large datasets by batching insertions
  const batchSize = 1000;
  for (let i = 0; i < storeIds.length; i += batchSize) {
    const batch = storeIds.slice(i, i + batchSize);
    const assignments = batch.map((storeId) => ({
      employee_id: employeeId,
      store_id: storeId,
      created_at: new Date(),
      updated_at: new Date(),
    }));

    await knex("store_assignment").insert(assignments);
  }
};

/**
 * Remove store assignments from an employee
 */
export const removeStoreAssignments = async (
  employeeId: string,
  storeIds?: string[]
): Promise<void> => {
  if (!employeeId) {
    throw new Error("Employee ID is required");
  }

  let query = knex("store_assignment").where({ employee_id: employeeId });

  if (storeIds && storeIds.length > 0) {
    query = query.whereIn("store_id", storeIds);
  }

  await query.delete();
};

/**
 * Get stores assigned to a specific employee
 */
export const getEmployeeStores = async (
  employeeId: string
): Promise<AssignableStore[]> => {
  if (!employeeId) {
    throw new Error("Employee ID is required");
  }

  const rows = await knex
    .select(
      "attain_user.id",
      "attain_user.name",
      "attain_user.address",
      // "attain_user.route_id",
      "attain_user.phone_number"
      // "route.name as route_name"
    )
    .from("store_assignment")
    .join("attain_user", "store_assignment.store_id", "attain_user.id")
    // .leftJoin("route", "attain_user.route_id", "route.id")
    .where("store_assignment.employee_id", employeeId)
    .where("attain_user.archived", false)
    .orderBy("attain_user.name");

  return rows.map((row) => ({
    id: row.id,
    name: row.name || "",
    address: row.address || "", // Handle null addresses
    // route_id: row.route_id,
    // route_name: row.route_name,
    phone_number: row.phone_number || "", // Handle null phone numbers
  }));
};

/**
 * Get employee assignments for filtering (used by mobile app)
 */
export const getEmployeeAssignments = async (
  employeeId: string
): Promise<{
  routeIds: string[];
  storeIds: string[];
  hasAllAccess: boolean;
}> => {
  if (!employeeId) {
    throw new Error("Employee ID is required");
  }

  // Get route assignments
  const routeAssignments = await knex("route_assignment")
    .select("route_id")
    .where("employee_id", employeeId);

  const routeIds = routeAssignments.map((r) => r.route_id.toString());

  // Get store assignments
  const storeAssignments = await knex("store_assignment")
    .select("store_id")
    .where("employee_id", employeeId);

  const storeIds = storeAssignments.map((s) => s.store_id.toString());

  // Check if employee has admin role (gets all access)
  const adminRole = await knex("role_assignment")
    .join("roles", "role_assignment.role_id", "roles.id")
    .where("role_assignment.employee_id", employeeId)
    .where("roles.name", "Admin")
    .first();

  const hasAllAccess = !!adminRole;

  return {
    routeIds,
    storeIds,
    hasAllAccess,
  };
};

export default {
  getAssignableStores,
  getAllStoreIds,
  getStoreDetailsByIds,
  assignStoresToEmployee,
  removeStoreAssignments,
  getEmployeeStores,
  getEmployeeAssignments,
};
