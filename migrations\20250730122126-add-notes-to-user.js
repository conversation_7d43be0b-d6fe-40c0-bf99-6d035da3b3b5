"use strict";

let dbm;
let type;
let seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db.addColumn("attain_user", "notes", {
    type: "text",
    default: null,
  });
};

exports.down = function (db) {
  return db.removeColumn("attain_user", "notes");
};

exports._meta = { version: 1 };
