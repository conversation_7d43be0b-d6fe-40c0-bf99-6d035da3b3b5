import dayjs from "dayjs";
import knex from "../../../knex/knex";
import { getOrders } from "../orderService/orderService";
import { getQbIds } from "../integrationService/quickbooksService";
import { getItems } from "../itemService/itemService";
import { Invoice } from "../../generated/graphql";
import addInvoiceItems from "./addInvoiceItems";
import deleteInvoiceItems from "./deleteInvoiceItems";

export default async function updateInvoiceFromOrder(
  supplierId: string,
  orderId: string,
  invoice: Partial<Invoice>
) {
  let order;

  try {
    const orderResult = await knex("order_detail")
      .select("order_number")
      .where("id", orderId)
      .first();

    if (orderResult) {
      const { orders } = await getOrders({
        filters: { ids: [orderResult.order_number.toString()] },
        supplierId,
      });

      if (orders.length > 0) {
        order = orders[0];
      }
    }
  } catch (error) {
    // Suppress error to allow the next lookup method to run
  }

  if (!order) {
    try {
      const { orders } = await getOrders({
        filters: { ids: [orderId] },
        supplierId,
      });
      if (orders.length > 0) order = orders[0];
    } catch (error) {
      // Suppress error before the final check
    }
  }

  if (!order) {
    throw new Error("Order not found. Unable to update invoice.");
  }

  const existingInvoice = await knex("invoice")
    .where("order_id", order.id)
    .first();

  if (!existingInvoice) {
    throw new Error(`No invoice found for order ID ${order.id}`);
  }

  // Separate delivery fee items from regular items
  const regularItems = order.orderItems.filter((item) => item.item_id !== null);
  const deliveryFeeItems = order.orderItems.filter(
    (item) => item.item_id === null
  );

  // Get QB IDs only for regular items
  const qb_ids =
    regularItems.length > 0
      ? await getQbIds(regularItems.map((item) => item.item_id.toString()))
      : new Map();

  const { items } = await getItems({
    supplierId,
    userId: order.customerDetails?.id,
    filters: {
      ids: regularItems.map((item) => item.item_id.toString()),
    },
  });

  // Create invoice items for regular items
  const invoice_items = regularItems.map((item) => {
    const invoiceItemMetadata = items.find(
      (itemData) => itemData.id === item.item_id
    )?.metadata;
    const invoiceItemName = invoiceItemMetadata
      ? `${item.name} (${invoiceItemMetadata})`
      : item.name;
    return {
      item_id: item.item_id.toString(),
      name: invoiceItemName,
      price: item.price_purchased_at ?? item.price,
      quantity: item.quantity,
      cog_price: item.cog_price,
      upc1: item.upc1,
      unit_size: item.unit_size,
      size: item.size,
      qb_id: qb_ids.get(item.item_id.toString()) ?? null,
      item_uom_id: item.item_uom_id,
    };
  });

  // Add delivery fee items
  deliveryFeeItems.forEach((deliveryItem) => {
    invoice_items.push({
      item_id: null,
      name: "Delivery Fee",
      price: deliveryItem.price_purchased_at ?? deliveryItem.price,
      quantity: deliveryItem.quantity,
      cog_price: null,
      upc1: null,
      unit_size: null,
      size: null,
      qb_id: null,
      item_uom_id: null,
    });
  });

  const subtotal = invoice_items
    .map((item) => item.price * item.quantity)
    .reduce((acc, itemTotal) => acc + itemTotal, 0);

  const trxProvider = knex.transactionProvider();
  const trx = await trxProvider();

  const updateableInvoiceFields = [
    "payment_method",
    "payment_status",
    "paid",
    "notes",
    "credit",
    "signature",
    "signature_name",
  ];

  const invoiceFieldsToUpdate = invoice
    ? Object.fromEntries(
        Object.entries(invoice).filter(([key]) =>
          updateableInvoiceFields.includes(key)
        )
      )
    : {};

  try {
    await trx("invoice")
      .update({
        ...invoiceFieldsToUpdate,
        subtotal: order.subtotal,
        discount: order.discount,
        date_created: order.delivery_date,
        total:
          subtotal -
          (order.discount || 0) -
          (invoiceFieldsToUpdate.credit || existingInvoice.credit || 0),
        notes: order.notes,
        updated_at: dayjs().toISOString(),
        archived: order.status === "Canceled",
        config: { ...(invoice?.config || {}), ...(order?.config || {}) },
      })
      .where("id", existingInvoice.id);

    // Delete all existing invoice items
    await deleteInvoiceItems(existingInvoice.id, [], true, trxProvider);

    // Add new invoice items based on order
    await addInvoiceItems(
      existingInvoice.id,
      invoice_items,
      false,
      trxProvider
    );

    await(await trxProvider()).commit();

    // Return the updated invoice
    const updatedInvoice = await knex("invoice")
      .select("*")
      .where("id", existingInvoice.id)
      .first();

    return updatedInvoice;
  } catch (error) {
    await(await trxProvider()).rollback();
    console.error("Error updating invoice from order:", error);
    throw new Error(
      `Error updating invoice for order ${order.id}: ${error.message}`
    );
  }
}
