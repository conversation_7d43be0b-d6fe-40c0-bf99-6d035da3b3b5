// src/services/accessService/employeeFilteringService.ts - Fixed version

import knex from "../../../knex/knex";
import { Route } from "../../generated/graphql";

export interface FilteredRoutes {
  routes: Route[];
  hasAllAccess: boolean;
}

export interface FilteredStores {
  stores: Array<{
    id: string;
    name: string;
    address: string;
    route_id?: string;
    phone_number?: string;
    approved: boolean;
    config: any;
  }>;
  hasAllAccess: boolean;
}

/**
 * Get filtered routes for an employee based on their assignments
 */
export const getEmployeeFilteredRoutes = async (
  employeeId: string,
  supplierId: string
): Promise<FilteredRoutes> => {
  if (!employeeId || !supplierId) {
    return {
      routes: [],
      hasAllAccess: false,
    };
  }

  try {
    // Check if employee has admin role (gets all access)
    const adminRole = await knex("role_assignment")
      .join("roles", "role_assignment.role_id", "roles.id")
      .where("role_assignment.employee_id", employeeId)
      .where("roles.name", "Admin")
      .first();

    const hasAllAccess = !!adminRole;

    // Get all routes for this supplier
    const allRoutes = await knex("route")
      .select("*")
      .where("supplier_id", supplierId)
      .orderBy("name");

    // If admin, return all routes
    if (hasAllAccess) {
      return {
        routes: allRoutes,
        hasAllAccess: true,
      };
    }

    // Get assigned routes for this employee
    const assignedRoutes = await knex("route_assignment")
      .join("route", "route_assignment.route_id", "route.id")
      .select("route.*")
      .where("route_assignment.employee_id", employeeId)
      .where("route.supplier_id", supplierId)
      .orderBy("route.name");

    return {
      routes: assignedRoutes,
      hasAllAccess: false,
    };
  } catch (error) {
    console.error("Error in getEmployeeFilteredRoutes:", error);
    return {
      routes: [],
      hasAllAccess: false,
    };
  }
};

/**
 * Get filtered stores for an employee based on their assignments
 */
export const getEmployeeFilteredStores = async (
  employeeId: string,
  supplierId: string,
  selectedRouteIds?: string[]
): Promise<FilteredStores> => {
  if (!employeeId || !supplierId) {
    console.error("Missing required parameters:", { employeeId, supplierId });
    return {
      stores: [],
      hasAllAccess: false,
    };
  }

  try {
    // Check if employee has admin role (gets all access)
    const adminRole = await knex("role_assignment")
      .join("roles", "role_assignment.role_id", "roles.id")
      .where("role_assignment.employee_id", employeeId)
      .where("roles.name", "Admin")
      .first();

    const hasAllAccess = !!adminRole;

    // Base query for active stores from this supplier
    let baseQuery = knex("attain_user")
      .select(
        "attain_user.id",
        "attain_user.name",
        "attain_user.address",
        "attain_user.route_id",
        "attain_user.phone_number",
        "attain_user.approved",
        "attain_user.config"
      )
      .where("attain_user.archived", false)
      .innerJoin(
        "supplier_times",
        "attain_user.id",
        "supplier_times.business_id"
      )
      .where("supplier_times.supplier_id", supplierId);

    // If admin, return all stores (optionally filtered by selected routes)
    if (hasAllAccess) {
      if (selectedRouteIds && selectedRouteIds.length > 0) {
        baseQuery = baseQuery.whereIn("attain_user.route_id", selectedRouteIds);
      }

      const allStores = await baseQuery.orderBy("attain_user.name");

      return {
        stores: allStores.map((store) => ({
          id: store.id,
          name: store.name || "",
          address: store.address || "",
          route_id: store.route_id,
          phone_number: store.phone_number || "",
          approved: store.approved || false,
          config: store.config || {},
        })),
        hasAllAccess: true,
      };
    }

    // For non-admin employees, get stores they have access to
    let accessibleStoreIds = new Set<string>();

    // 1. Get stores from assigned routes
    if (selectedRouteIds && selectedRouteIds.length > 0) {
      // Check if employee has access to these specific routes
      const employeeRoutes = await knex("route_assignment")
        .select("route_id")
        .where("employee_id", employeeId)
        .whereIn("route_id", selectedRouteIds);

      if (employeeRoutes.length > 0) {
        const allowedRouteIds = employeeRoutes.map((r) => r.route_id);

        // use supplier_times join instead of suppliers array
        const storesFromRoutes = await knex("attain_user")
          .select("attain_user.id")
          .innerJoin(
            "supplier_times",
            "attain_user.id",
            "supplier_times.business_id"
          )
          .where("attain_user.archived", false)
          .where("supplier_times.supplier_id", supplierId)
          .whereIn("attain_user.route_id", allowedRouteIds);

        storesFromRoutes.forEach((store) =>
          accessibleStoreIds.add(store.id.toString())
        );
      }
    } else {
      // If no specific routes selected, get all stores from employee's assigned routes
      const employeeRoutes = await knex("route_assignment")
        .select("route_id")
        .where("employee_id", employeeId);

      if (employeeRoutes.length > 0) {
        const routeIds = employeeRoutes.map((r) => r.route_id);

        // Fixed query - use supplier_times join instead of suppliers array
        const storesFromRoutes = await knex("attain_user")
          .select("attain_user.id")
          .innerJoin(
            "supplier_times",
            "attain_user.id",
            "supplier_times.business_id"
          )
          .where("attain_user.archived", false)
          .where("supplier_times.supplier_id", supplierId)
          .whereIn("attain_user.route_id", routeIds);

        storesFromRoutes.forEach((store) =>
          accessibleStoreIds.add(store.id.toString())
        );
      }
    }

    // 2. Get directly assigned stores
    const directlyAssignedStores = await knex("store_assignment")
      .select("store_id")
      .where("employee_id", employeeId);

    directlyAssignedStores.forEach((store) =>
      accessibleStoreIds.add(store.store_id.toString())
    );

    // If no accessible stores found, return empty list
    if (accessibleStoreIds.size === 0) {
      return {
        stores: [],
        hasAllAccess: false,
      };
    }

    // Get full store details for accessible stores
    const accessibleStores = await knex("attain_user")
      .select(
        "id",
        "name",
        "address",
        "route_id",
        "phone_number",
        "approved",
        "config"
      )
      .where("archived", false)
      .whereIn("id", Array.from(accessibleStoreIds))
      .orderBy("name");

    return {
      stores: accessibleStores.map((store) => ({
        id: store.id,
        name: store.name || "",
        address: store.address || "",
        route_id: store.route_id,
        phone_number: store.phone_number || "",
        approved: store.approved || false,
        config: store.config || {},
      })),
      hasAllAccess: false,
    };
  } catch (error) {
    console.error("Error in getEmployeeFilteredStores:", error);
    return {
      stores: [],
      hasAllAccess: false,
    };
  }
};

export default {
  getEmployeeFilteredRoutes,
  getEmployeeFilteredStores,
};
