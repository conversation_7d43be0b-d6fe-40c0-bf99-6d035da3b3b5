import { Knex } from "knex";
import {
  Order,
  Ordering,
  OrdersFiltersV2,
  PaginationInput,
  SortBy,
} from "../../generated/graphql";
import { getUsersScheduledOnDay } from "../userService/userService";
import knex from "../../../knex/knex";
import dayjs from "../../util/dayjsConfig";
import modifyToCustomPricing from "../itemService/modifyToCustomPricing";
import {
  populateCartItemsUOMs,
  populateInvoiceItemsUOMs,
} from "../itemService/uomService";
import { getPromotionUsageForOrders } from "../promotionService";
import salesRepService from "../salesRepService";

// Create accessors for JSON and other fields
function convertToQuery(inputField: string) {
  const [field, key] = inputField.split(".");
  if (field === "customerDetails") {
    return `"${field}" ->> '${key}'`;
  } else if (key) {
    return field + "_" + key;
  }
  return field;
}

export const getPopulatedOrders = async (supplierId: string, orders) => {
  const orderIds = orders.map((order) => order.id);

  // Get order items with LEFT JOIN to include delivery fees
  const orderItems = await knex
    .select([
      "order_item.id as id",
      "order_item.order_id",
      "order_item.item_id",
      "order_item.quantity",
      "order_item.price_purchased_at",
      "order_item.notes",
      "order_item.item_uom_id",
      "order_item.oos",
      "item.name",
      "item.unit_size",
      "item.size",
      "item.upc1",
      "item.upc2",
      "item.price",
      "item.cog_price",
      "item.image",
      "item.supplier_code",
      "item.supplier",
      "item.metadata",
      "item.qb_id",
      "item.sorting_order",
      "item.nacs_category",
      "item.nacs_subcategory",
      "item.crv",
      "item.qoh",
      "item.discounted_price",
      "item.archived",
    ])
    .from("order_item")
    .leftJoin("item", "item.id", "order_item.item_id")
    .whereIn("order_item.order_id", orderIds)
    .orderByRaw("COALESCE(item.sorting_order, 999999) ASC")
    .then((items) =>
      items.map((item) => ({
        ...item,
        // The id field is now explicitly order_item.id (no override from item.id)
        // Override name for delivery fees
        name: item.item_id === null ? "Delivery Fee" : item.name,
        // Ensure other fields have defaults for delivery fees
        unit_size: item.unit_size || "",
        size: item.size || "",
        upc1: item.upc1 || "",
        upc2: item.upc2 || "",
        price: item.price || item.price_purchased_at,
        cog_price: item.cog_price || 0,
        image: item.image || "",
        supplier_code: item.supplier_code || "",
        supplier: item.supplier || "",
        metadata: item.metadata || "",
        qb_id: item.qb_id || "",
        sorting_order: item.sorting_order || 999999,
      }))
    );

  const invoices = await knex
    .select("*")
    .from("invoice")
    .whereIn("order_id", orderIds);

  // Get promotion usage records for these orders
  const enrichedPromotionUsage = await getPromotionUsageForOrders(
    supplierId,
    orderIds
  );
  const invoiceItems = await knex
    .select("*")
    .from("invoice_item")
    .whereIn(
      "invoice_id",
      invoices.map((inv) => inv.id)
    );

  // Populate UOM data for invoice items
  await populateInvoiceItemsUOMs(invoiceItems);

  const itemsByOrderId = {};
  orderItems.forEach((item) => {
    if (!itemsByOrderId[item.order_id]) {
      itemsByOrderId[item.order_id] = [];
    }
    itemsByOrderId[item.order_id].push(item);
  });

  const invoiceItemsByInvoiceId = {};
  invoiceItems.forEach((item) => {
    if (!invoiceItemsByInvoiceId[item.invoice_id]) {
      invoiceItemsByInvoiceId[item.invoice_id] = [];
    }
    invoiceItemsByInvoiceId[item.invoice_id].push(item);
  });

  const result = Promise.all(
    orders.map(async (order) => {
      if (order.user_id && order.id in itemsByOrderId) {
        await modifyToCustomPricing(itemsByOrderId[order.id], order.user_id);
      }

      // Populate UOM data for order items
      if (order.id in itemsByOrderId) {
        await populateCartItemsUOMs(itemsByOrderId[order.id]);
      }

      // Calculate overdue status
      let calculatedStatus = order.status;
      const invoice = invoices.find((inv) => inv.order_id === order.id);

      if (
        order.net_terms_days !== null &&
        invoice &&
        order.delivery_date &&
        supplierId != "68" // Whitestone doesn't track paid amounts in our system
      ) {
        const amountDue =
          Number(invoice.total || 0) - Number(invoice.paid || 0);
        if (amountDue > 0 && calculatedStatus == "Delivered") {
          // Calculate due date: delivery date + net terms days
          const deliveryDate = dayjs(order.delivery_date);
          const dueDate = deliveryDate.add(order.net_terms_days, "day");
          const now = dayjs();

          if (now.isAfter(dueDate)) {
            calculatedStatus = "Overdue";
          }
        }
      }

      return {
        id: order.id,
        order_number: order.order_number,
        status: calculatedStatus,
        orderItems: itemsByOrderId[order.id] || [],
        orderName: order.order_name,
        subtotal: order.subtotal,
        discount: order.discount,
        date_submitted: order.date_submitted,
        delivery_date: order.delivery_date,
        supplier: order.single_supplier,
        supplier_logo: order.logo,
        notification_number: order.notification_number,
        notification_email: order.notification_email,
        customerDetails: order.customerDetails,
        invoice: (() => {
          const invoice = invoices.find(
            (invoice) => invoice.order_id === order.id
          );
          if (invoice) {
            return {
              ...invoice,
              invoiceItems: invoiceItemsByInvoiceId[invoice.id] || [],
            };
          }
          return null;
        })(),
        totalQuantity: (itemsByOrderId[order.id] || []).reduce(
          (totalQty, item) => totalQty + item.quantity,
          0
        ),
        notes: order.notes,
        config: order.config,
        promotions: enrichedPromotionUsage.filter(
          (promotion) => promotion.order_id === order.id
        ),
      };
    })
  );

  return result;
};

export const getOrders = async ({
  filters = { includeArchivedStores: false },
  supplierId,
  pagination = { offset: 0, limit: 50 },
  sortBy = { field: "id", ordering: Ordering.Desc },
}: {
  filters?: {
    ids?: string[];
    status?: string;
    deliveryDate?: Date;
    deliveryDateRange?: Date[];
    routeIds?: string[];
    driver?: string;
    userIds?: string[];
    userId?: string; // TODO: remove this case and only use userIds
    signed?: boolean;
    query?: string;
    lastPaidDate?: Date;
    includeArchivedStores?: boolean;
    perUserLimit?: number;
    paidStatus?: string;
    invoiceIds?: string[];
    employee_ids?: string[];
    userStatus?: string;
  };
  supplierId: string;
  pagination?: { offset?: number; limit?: number };
  sortBy?: { field?: string; ordering?: Ordering };
}): Promise<{ orders: Order[]; totalCount: number }> => {
  if (!supplierId) {
    throw new Error("Supplier ID is required");
  }

  const sortFieldAccessor = convertToQuery(sortBy.field); // Sorting accessors for field types JSON and columns

  const filter = (queryBuilder: Knex.QueryBuilder) => {
    if (filters?.ids) {
      queryBuilder.whereIn("order_detail.order_number", filters.ids);
    }
    if (filters?.status) {
      switch (filters.status.toLowerCase()) {
        case "placed":
          queryBuilder.where("status", "ilike", "submitted");
          break;
        case "overdue":
          queryBuilder
            .where(function () {
              this.where("status", "ilike", "submitted").orWhere(
                "status",
                "ilike",
                "in transit"
              );
            })
            .andWhereRaw("delivery_date < CURRENT_DATE");
          break;
        case "invoiced":
          queryBuilder
            .where("status", "ilike", "in transit")
            .whereNotNull("invoice.id");
          break;
        default:
          queryBuilder.where("status", "ilike", filters.status);
          break;
      }
    }
    if (filters?.deliveryDate) {
      queryBuilder.andWhere(
        "delivery_date",
        dayjs(filters.deliveryDate).utc().format("YYYY-MM-DD")
      );
    }
    if (filters?.deliveryDateRange) {
      queryBuilder
        .andWhere(
          "delivery_date",
          ">=",
          dayjs(filters.deliveryDateRange[0]).utc().format("YYYY-MM-DD")
        )
        .andWhere(
          "delivery_date",
          "<=",
          dayjs(filters.deliveryDateRange[1]).utc().format("YYYY-MM-DD")
        );
    }
    if ((filters?.routeIds && filters?.routeIds.length) || filters?.driver) {
      queryBuilder.where((builder) => {
        if (filters?.driver) {
          // First check if there's a custom_driver on the order
          builder.where((subBuilder) => {
            subBuilder.whereRaw(
              `order_detail.config->>'custom_driver' = ?`,
              filters.driver
            );
          });
          // For orders with no custom_driver, check route_ids
          builder.orWhere((subBuilder) => {
            subBuilder
              .whereRaw(`order_detail.config->>'custom_driver' IS NULL`)
              .andWhere((routeBuilder) => {
                if (filters?.routeIds && filters?.routeIds.length) {
                  routeBuilder.where(
                    knex.raw(
                      `?::text[] && string_to_array(attain_user.route_id, ',')`,
                      [filters.routeIds]
                    )
                  );
                  routeBuilder.orWhereRaw(
                    `order_detail.config->>'custom_route' = ANY(?::text[])`,
                    [filters.routeIds]
                  );
                }
              });
          });
        } else if (filters?.routeIds && filters?.routeIds.length) {
          // If using route instead of driver, check routes
          // First check if there's a custom_route on the order
          builder.where((subBuilder) => {
            subBuilder.whereRaw(
              `order_detail.config->>'custom_route' = ANY(?::text[])`,
              [filters.routeIds]
            );
          });
          // For orders with no custom_route, check route_ids
          builder.orWhere((subBuilder) => {
            subBuilder
              .whereRaw(`order_detail.config->>'custom_route' IS NULL`)
              .andWhere((routeBuilder) => {
                if (filters?.routeIds && filters?.routeIds.length) {
                  routeBuilder.where(
                    knex.raw(
                      `?::text[] && string_to_array(attain_user.route_id, ',')`,
                      [filters.routeIds]
                    )
                  );
                }
              });
          });
        }
      });
    }

    if (filters?.employee_ids && filters?.employee_ids.length) {
      queryBuilder.where(function () {
        this.whereExists(function () {
          this.select(knex.raw(1))
            .from("route")
            .join("route_assignment", "route.id", "route_assignment.route_id")
            .join("employees", "route_assignment.employee_id", "employees.id")
            .whereRaw("route.id = attain_user.primary_route_id")
            .whereIn("employees.id", filters.employee_ids)
            .where("employees.archived", false);
        }).orWhereExists(function () {
          this.select(knex.raw(1))
            .from("route")
            .join("route_assignment", "route.id", "route_assignment.route_id")
            .join("employees", "route_assignment.employee_id", "employees.id")
            .whereRaw(
              "route.id::text = ANY(string_to_array(attain_user.route_id, ','))"
            )
            .whereIn("employees.id", filters.employee_ids)
            .where("employees.archived", false)
            .where(function () {
              this.whereNull("attain_user.primary_route_id");
            });
        });
      });
    }

    // TODO: remove the userId case and only user userIds
    if (
      filters?.userIds &&
      Array.isArray(filters.userIds) &&
      filters.userIds.length > 0
    ) {
      queryBuilder.whereIn("order_detail.user_id", filters.userIds);
    } else if (filters?.userId) {
      queryBuilder.andWhere("order_detail.user_id", filters.userId);
    }
    if (filters?.signed !== undefined && filters.signed !== null) {
      queryBuilder.andWhere(
        "invoice.signature_name",
        filters.signed ? "is not" : "is",
        null
      );
    }
    if (filters?.query) {
      const isNumericQuery = /^\d+$/.test(filters.query);
      queryBuilder.andWhere(function () {
        if (isNumericQuery && filters.query.length < 6) {
          this.where("order_detail.notes", "ilike", `%${filters.query}%`)
            .orWhere("order_detail.order_number", filters.query)
            .orWhere("invoice.order_number", filters.query);
        } else {
          this.where("order_detail.notes", "ilike", `%${filters.query}%`);
        }
      });
    }
    if (filters?.lastPaidDate) {
      queryBuilder.whereRaw(
        "invoice.config->>'date_last_paid' = ?",
        dayjs(filters.lastPaidDate).utc().format("YYYY-MM-DD")
      );
    }
    if (!filters?.includeArchivedStores) {
      queryBuilder.andWhere("attain_user.archived", false);
    }
    if (filters?.userStatus) {
      switch (filters.userStatus.toLowerCase()) {
        case "active":
          queryBuilder.andWhere("attain_user.archived", false);
          break;
        case "inactive":
          queryBuilder.andWhere("attain_user.archived", true);
          break;
      }
    }
    if (filters?.paidStatus) {
      switch (filters.paidStatus.toLowerCase()) {
        case "paid":
          queryBuilder.whereRaw(
            `ROUND(coalesce(invoice.total, 0)-coalesce(invoice.paid, 0), 2) <= 0`
          );
          break;
        case "unpaid":
          queryBuilder.whereRaw(
            `ROUND(coalesce(invoice.total, 0)-coalesce(invoice.paid, 0), 2) > 0`
          );
          break;
        case "bounced":
          queryBuilder.where("invoice.payment_status", "bounced");
          break;
        case "partial":
          queryBuilder.whereRaw(
            `ROUND(coalesce(invoice.paid, 0), 2) > 0 AND ROUND(coalesce(invoice.total, 0)-coalesce(invoice.paid, 0), 2) > 0`
          );
          break;
        case "overdue":
          queryBuilder
            .whereRaw(
              `COALESCE(invoice.total, 0)-coalesce(invoice.paid, 0) > 0`
            )
            .whereRaw(`order_detail.net_terms_days IS NOT NULL`)
            .whereRaw(`order_status.delivery_date IS NOT NULL`)
            .where("order_detail.status", "ilike", "delivered")
            .whereRaw(
              `CURRENT_DATE > (order_status.delivery_date::date + INTERVAL '1 day' * order_detail.net_terms_days)`
            );
          break;
      }
    }
    if (filters?.invoiceIds) {
      queryBuilder.whereIn("invoice.id", filters.invoiceIds);
    }
  };

  const supplierName = (
    await knex.select("name").from("supplier").where("id", supplierId)
  )[0].name;

  const baseQuery = knex
    .select(
      "order_status.delivery_date",
      "order_status.delivering_date",
      "order_status.submission_date",
      "order_detail.*",
      "supplier.logo",
      "invoice.id as invoice_id",
      knex.raw(
        `json_build_object('id', "attain_user".id, 'name', "attain_user".name, 'email', "attain_user".email, 
        'contact_email', "attain_user".contact_email,
        'phone_number', "attain_user".phone_number, 'address', "attain_user".address, 'route_id', "attain_user".route_id, 'primary_route_id', "attain_user".primary_route_id, 'custom_prices', coalesce("cp".custom_prices, '[]'::json), 'custom_uom_prices', coalesce("cuom".custom_uom_prices, '[]'::json)) as "customerDetails"`
      )
    )
    .from("order_status")
    .rightOuterJoin("order_detail", "order_status.order_id", "order_detail.id")
    .leftOuterJoin("supplier", "order_detail.single_supplier", "supplier.name")
    .leftOuterJoin("attain_user", "order_detail.user_id", "attain_user.id")
    .leftOuterJoin(
      knex.raw(
        "(SELECT user_id, json_agg(json_build_object('item_id', item_id, 'price', price)) as custom_prices FROM custom_item_price GROUP BY user_id) cp USING(user_id)"
      )
    )
    .leftOuterJoin(
      knex.raw(
        `(SELECT 
          custom_item_uom_price.user_id, 
          json_agg(
            json_build_object(
              'item_id', item_uom.item_id,
              'uom_prices', json_build_array(
                json_build_object(
                  'item_uom_id', custom_item_uom_price.item_uom_id,
                  'user_id', custom_item_uom_price.user_id,
                  'price', custom_item_uom_price.price,
                  'uom_name', uom.name,
                  'uom_id', uom.id
                )
              )
            )
          ) as custom_uom_prices 
        FROM custom_item_uom_price 
        INNER JOIN item_uom ON custom_item_uom_price.item_uom_id = item_uom.id
        INNER JOIN uom ON item_uom.uom_id = uom.id
        WHERE uom.supplier_id = ${supplierId}
        GROUP BY custom_item_uom_price.user_id) cuom USING(user_id)`
      )
    )
    .leftOuterJoin("invoice", "order_detail.id", "invoice.order_id")
    .where("single_supplier", supplierName)
    .modify(filter);

  const fullQuery = knex
    .with("orders", baseQuery)
    .select("*")
    .from("orders")
    .orderByRaw(`("orders".` + sortFieldAccessor + ") " + sortBy.ordering) // Some string munging for accessing different field types
    .limit(pagination.limit)
    .offset(pagination.offset)
    .rightJoin(
      knex.raw("(SELECT count(*) as total_count FROM orders) c ON true")
    );

  // Apply perUserLimit filter if specified
  let result;
  if (filters?.perUserLimit && filters.perUserLimit > 0) {
    // Create a window function to partition by user_id, ordering by the sort field
    const windowQuery = knex
      .with(
        "numbered_orders",
        knex.raw(`
        SELECT 
          *,
          ROW_NUMBER() OVER (
            PARTITION BY user_id 
            ORDER BY ${sortFieldAccessor} ${sortBy.ordering}
          ) as row_num
        FROM (${baseQuery.toString()}) as orders
      `)
      )
      .select("*")
      .from("numbered_orders")
      .where("row_num", "<=", filters.perUserLimit)
      .orderByRaw(`(${sortFieldAccessor}) ${sortBy.ordering}`)
      .limit(pagination.limit)
      .offset(pagination.offset)
      .rightJoin(
        knex.raw(`
          (SELECT count(*) as total_count FROM numbered_orders WHERE row_num <= ${filters.perUserLimit}) c ON true
        `)
      );
    result = await windowQuery;
  } else {
    result = await fullQuery;
  }

  const orders = await getPopulatedOrders(
    supplierId,
    result.filter((order) => order.id)
  );
  const ordersWithSalesRep = await salesRepService.addSalesRepToOrders(orders);

  const totalCount =
    result && result.length > 0 ? parseInt(result[0]?.total_count, 10) : 0;

  return { orders: ordersWithSalesRep, totalCount };
};

export const getExpectedOrders = async (
  supplierId: string,
  date: Date
): Promise<Order[]> => {
  const selectedDate = date ?? new Date();
  const usersOnScheduledDay = await getUsersScheduledOnDay(
    supplierId,
    selectedDate
  );
  const { orders } = await getOrders({
    supplierId,
    filters: {
      deliveryDate: selectedDate,
      userIds: usersOnScheduledDay.map((user) => user.id),
    },
    pagination: { offset: 0, limit: 500 },
  });
  const expectedOrders = usersOnScheduledDay
    .filter(
      (user) => !orders.some((order) => order.customerDetails?.id === user.id)
    )
    .map((user) => ({
      id: user.id,
      customerDetails: user,
      status: "Expected",
      delivery_date: dayjs(selectedDate).toDate(),
      subtotal: 0,
    }));
  return expectedOrders;
};
