import { Request, Response } from "express";
import { ParsedQs } from "qs";
import { Invoice, Route, Supplier } from "../generated/graphql";
import generateInvoiceZPLString, {
  TableColumnWidths,
  TotalsRows,
} from "../lib/invoiceZPL";
import RestEndpoint from "./_restEndpoint";
import { getSupplierConfig } from "../services/supplierService";

export default class GenerateInvoiceZPL extends RestEndpoint {
  private generateTableColumnWidths: (
    supplierId: string
  ) => [number, TableColumnWidths] = (supplierId: string) => {
    return [
      supplierId === "31" ? 65 : 60,
      {
        name: {
          title: "product",
          width: 28,
          pad: "right",
          format: null,
        },
        ...(supplierId === "31"
          ? {
              unit_size: {
                title: "ct",
                width: 4,
                pad: "left",
                format: null,
              },
            }
          : {}),
        quantity: {
          title: "qty",
          width: 5,
          pad: "left",
          format: null,
        },
        price: {
          title: "price",
          width: 11,
          pad: "left",
          format: "currency",
        },
        total: {
          title: "total",
          width: supplierId === "31" ? 14 : 13,
          pad: "left",
          format: "currency",
        },
      },
    ];
  };
  private totalsRows: (supplierId: string) => TotalsRows = (supplierId) => {
    return {
      subtotal: {
        title: "sales",
      },
      discount: {
        title: "discount",
      },
      credit: {
        title: "credits",
      },
      ...(supplierId === "31"
        ? {
            paid: {
              title: "paid",
            },
          }
        : {}),
      total: {
        title: "total due",
      },
    };
  };
  public async handler(req: Request, res: Response) {
    if (!req.query.invoiceId) {
      res.status(400).send("Missing invoiceId");
      return;
    }
    try {
      const zpl = await this.worker(req.query);
      console.log(zpl);
      res.status(200).send({ zpl });
    } catch (err) {
      const errMessage = `Failed to generate ZPL for invoice: ${
        err.message || err
      }`;
      console.error(errMessage);
      res.status(err.code || 500).send(errMessage);
    }
  }

  protected async worker(queryParams: ParsedQs) {
    const invoice = await this.getInvoice(queryParams.invoiceId as string);
    const supplier = await this.getSupplier(invoice.supplier_id);
    const supplierConfig = await getSupplierConfig(supplier.id);

    const scaleHeight = queryParams.scaleHeight
      ? parseFloat(queryParams.scaleHeight as string)
      : 1;

    const scaleWidth = queryParams.scaleWidth
      ? parseFloat(queryParams.scaleWidth as string)
      : supplierConfig.invoice_scale_width || 1.0;

    if (
      supplierConfig.pavilions_display_name &&
      (invoice.customerDetails.name?.includes("PV-") ||
        invoice.customerDetails.name?.includes("0 PAVILIONS"))
    ) {
      supplier.name = supplierConfig.pavilions_display_name;
      supplier.address = supplierConfig.pavilions_address || supplier.address;
    }

    const routes = invoice.customerDetails.route_id
      ? await this.getRoutes(invoice.customerDetails.route_id.split(","))
      : [];
    const [tableMaxWidth, tableColumnWidths] = this.generateTableColumnWidths(
      supplier.id
    );
    const invoiceZPLString = generateInvoiceZPLString(
      invoice,
      supplier,
      routes,
      tableColumnWidths,
      tableMaxWidth,
      this.totalsRows(supplier.id),
      scaleHeight,
      scaleWidth
    );
    return invoiceZPLString;
  }

  private async getInvoice(invoiceId: string) {
    const variables = {
      getInvoicesInput: {
        ids: [invoiceId],
        pagination: {
          offset: 0,
          limit: 1,
        },
      },
    };

    const query = /* GraphQL */ `
      query Invoice($getInvoicesInput: GetInvoicesInput) {
        invoices(getInvoicesInput: $getInvoicesInput) {
          customerDetails {
            name
            address
            phone_number
            route_id
          }
          date_created
          date_received
          invoiceItems {
            id
            price
            name
            quantity
            upc1
            unit_size
            metadata
            item_uom_id
            uoms {
              id
              name
              supplier_id
              uom_id
              quantity
              item_id
              archived
            }
          }
          id
          order_id
          invoice_id
          supplier_id
          total
          subtotal
          discount
          paid
          signature
          signature_name
          credit
          notes
        }
      }
    `;
    const invoices = (
      (await this.apolloHttpPost(query, variables)) as {
        invoices: Invoice[];
      }
    ).invoices;

    if (!invoices || invoices.length === 0) {
      throw { code: 400, message: `No invoice with ID ${invoiceId} found` };
    }

    return invoices[0];
  }

  private async getSupplier(supplierId: string) {
    const variables = {
      getSuppliersInput: {
        ids: [supplierId],
        pagination: {
          offset: 0,
          limit: 1,
        },
      },
    };

    const query = /* GraphQL */ `
      query Supplier($getSuppliersInput: GetSuppliersInput) {
        suppliers(getSuppliersInput: $getSuppliersInput) {
          id
          name
          address
          phone_number
          email
        }
      }
    `;
    const suppliers = (
      (await this.apolloHttpPost(query, variables)) as {
        suppliers: Supplier[];
      }
    ).suppliers;

    if (!suppliers || suppliers.length === 0) {
      throw `No supplier with ID ${supplierId} found`;
    }

    return suppliers[0];
  }

  private async getRoutes(routeIds: string[]) {
    const variables = {
      getRoutesInput: {
        ids: routeIds,
      },
    };

    const query = /* GraphQL */ `
      query Route($getRoutesInput: GetRoutesInput) {
        routes(getRoutesInput: $getRoutesInput) {
          id
          name
          color
          supplier_id
          day_of_week
        }
      }
    `;

    const routes = (
      (await this.apolloHttpPost(query, variables)) as {
        routes: Route[];
      }
    ).routes;

    if (!routes || routes.length === 0) {
      throw `No route with ID(s) ${routeIds} found`;
    }

    return routes;
  }
}
