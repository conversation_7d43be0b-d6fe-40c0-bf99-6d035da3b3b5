import { <PERSON><PERSON> } from "knex";
import knex from "../../../knex/knex";
import { InvoiceItem, InvoiceItemInput } from "../../generated/graphql";
import { getQbIds } from "../integrationService/quickbooksService";
const addInvoiceItems = async (
  invoiceId: string | undefined | null,
  items: InvoiceItemInput[],
  isReturning = false,
  trxProvider?: Knex.TransactionProvider | undefined | null
) => {
  const trxOrQuery = trxProvider
    ? (await trxProvider())("invoice_item")
    : knex("invoice_item");

  // Filter out delivery fee items for QB ID lookup
  const regularItems = items.filter((item) => item.item_id !== null);

  const qb_ids =
    regularItems.length > 0
      ? await getQbIds(regularItems.map((item) => item.item_id.toString()))
      : new Map();

  const updatedItems = items.map((item) => ({
    ...item,
    qb_id: item.item_id ? item.qb_id ?? qb_ids.get(item.item_id) ?? null : null, // No QB ID for delivery fees
  }));

  const invoiceItems: InvoiceItem[] = await trxOrQuery
    .insert(
      invoiceId
        ? updatedItems.map((item) => ({
            ...item,
            invoice_id: invoiceId,
          }))
        : updatedItems
    )
    .returning(isReturning ? "*" : ["id"]);

  return invoiceItems;
};

export default addInvoiceItems;
