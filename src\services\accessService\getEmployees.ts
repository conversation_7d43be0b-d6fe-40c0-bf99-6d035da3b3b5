import { K<PERSON> } from "knex";
import knex from "../../../knex/knex";
import { PaginationInput, SortBy, Ordering } from "../../generated/graphql";

export interface Employee {
  id: string;
  name: string;
  phone: string;
  email: string;
  app_access: boolean;
  dashboard_access: boolean;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
  archived: boolean;
  roles?: Array<{
    id: string;
    name: string;
    description: string;
  }>;
  routes?: Array<{
    id: string;
    name: string;
    color: string;
    day_of_week: string;
    driver: string;
    supplier_id: string;
    config: any;
  }>;
  stores?: Array<{
    id: string;
    name: string;
    address: string;
  }>;
  storeCount?: number;
  routeCount?: number;
  allStoreIds?: string[];
}

export interface EmployeeFilters {
  supplierId: string;
  ids?: string[];
  name?: string;
  email?: string;
  phone?: string;
  appAccess?: boolean;
  dashboardAccess?: boolean;
  roleIds?: string[];
  includeArchived?: boolean;
  query?: string; // For searching across name, email, phone
  createdAfter?: Date;
  createdBefore?: Date;
  lastLoginAfter?: Date;
  lastLoginBefore?: Date;
}

/**
 * Helper function to apply filters to a query builder
 */
const applyEmployeeFilters = (query: any, filters?: EmployeeFilters) => {
  if (!filters) return query;

  if (filters.ids && filters.ids.length > 0) {
    query = query.whereIn("employees.id", filters.ids);
  }

  if (filters.name) {
    query = query.where("employees.name", "ilike", `%${filters.name}%`);
  }

  if (filters.email) {
    query = query.where("employees.email", "ilike", `%${filters.email}%`);
  }

  if (filters.phone) {
    query = query.where("employees.phone", "ilike", `%${filters.phone}%`);
  }

  if (filters.appAccess !== undefined) {
    query = query.where("employees.app_access", filters.appAccess);
  }

  if (filters.dashboardAccess !== undefined) {
    query = query.where("employees.dashboard_access", filters.dashboardAccess);
  }

  if (filters.roleIds && filters.roleIds.length > 0) {
    query = query.whereExists(function () {
      this.select(knex.raw(1))
        .from("role_assignment")
        .whereRaw("role_assignment.employee_id = employees.id")
        .whereIn("role_assignment.role_id", filters.roleIds);
    });
  }

  // By default, exclude archived employees unless explicitly included
  if (!filters.includeArchived) {
    query = query.where("employees.archived", false);
  }

  // Generic search query across multiple fields
  if (filters.query) {
    query = query.where(function () {
      this.where("employees.name", "ilike", `%${filters.query}%`)
        .orWhere("employees.email", "ilike", `%${filters.query}%`)
        .orWhere("employees.phone", "ilike", `%${filters.query}%`);
    });
  }

  // Date range filters
  if (filters.createdAfter) {
    query = query.where("employees.created_at", ">=", filters.createdAfter);
  }

  if (filters.createdBefore) {
    query = query.where("employees.created_at", "<=", filters.createdBefore);
  }

  if (filters.lastLoginAfter) {
    query = query.where("employees.last_login", ">=", filters.lastLoginAfter);
  }

  if (filters.lastLoginBefore) {
    query = query.where("employees.last_login", "<=", filters.lastLoginBefore);
  }

  return query;
};

/**
 * Get employees with optional filtering, sorting, and pagination
 */
export const getEmployees = async ({
  filters,
  pagination = { offset: 0, limit: 50 },
  sortBy = { field: "id", ordering: Ordering.Desc },
}: {
  filters?: EmployeeFilters;
  pagination?: PaginationInput;
  sortBy?: SortBy;
}): Promise<{ employees: Employee[]; totalCount: number }> => {
  // Build the base query with roles
  const baseQuery = knex
    .select(
      "employees.*",
      knex.raw(
        `COALESCE(
          json_agg(
            DISTINCT jsonb_build_object(
              'id', roles.id,
              'name', roles.name,
              'description', roles.description
            )
          ) FILTER (WHERE roles.id IS NOT NULL),
          '[]'
        ) as roles`
      )
    )
    .from("employees")
    .leftJoin("role_assignment", "employees.id", "role_assignment.employee_id")
    .leftJoin("roles", "role_assignment.role_id", "roles.id")
    .where("employees.supplier_id", filters?.supplierId)
    .groupBy("employees.id");

  // Apply filters to base query
  const filteredQuery = applyEmployeeFilters(baseQuery, filters);

  // Create separate count query
  let countQuery = knex("employees").where(
    "employees.supplier_id",
    filters?.supplierId
  );

  // Apply the same filters to count query
  countQuery = applyEmployeeFilters(countQuery, filters);

  // Execute count query
  const countResult = await countQuery.count("* as count").first();
  const totalCount = parseInt((countResult as any)?.count || "0", 10);

  // Apply sorting and pagination to main query
  const paginatedQuery = filteredQuery
    .orderBy(`employees.${sortBy.field}`, sortBy.ordering.toLowerCase())
    .offset(pagination.offset || 0)
    .limit(pagination.limit || 50);

  const rows = await paginatedQuery;

  // Map rows to typed Employee objects with roles
  const employees: Employee[] = rows.map((row) => ({
    id: row.id,
    name: row.name,
    phone: row.phone,
    email: row.email,
    app_access: row.app_access,
    dashboard_access: row.dashboard_access,
    created_at: row.created_at,
    updated_at: row.updated_at,
    last_login: row.last_login,
    archived: row.archived,
    roles: row.roles,
    routes: [],
    stores: [],
    routeCount: 0,
    storeCount: 0,
  }));

  // Get only COUNTS for routes and stores, not full objects
  if (employees.length > 0) {
    const employeeIds = employees.map((emp) => emp.id);

    // Get route counts for each employee
    const routeCounts = await knex("route_assignment")
      .select("employee_id")
      .count("route_id as count")
      .whereIn("employee_id", employeeIds)
      .groupBy("employee_id");

    // Get full route details
    const routesData = await knex
      .select(
        "route.id",
        "route.name",
        "route.color",
        "route.day_of_week",
        "route.driver",
        "route.supplier_id",
        "route.config",
        "route_assignment.employee_id"
      )
      .from("route_assignment")
      .join("route", "route_assignment.route_id", "route.id")
      .whereIn("route_assignment.employee_id", employeeIds);

    // Group routes by employee
    const routesByEmployee: Record<string, Array<any>> = {};
    routesData.forEach((route) => {
      if (!routesByEmployee[route.employee_id]) {
        routesByEmployee[route.employee_id] = [];
      }

      routesByEmployee[route.employee_id].push({
        id: route.id,
        name: route.name,
        color: route.color,
        day_of_week: route.day_of_week,
        driver: route.driver,
        supplier_id: route.supplier_id,
        config: route.config,
      });
    });

    // Add routes to employees
    employees.forEach((employee) => {
      employee.routes = routesByEmployee[employee.id] || [];
    });

    // Get store counts for each employee
    const storeCounts = await knex("store_assignment")
      .select("employee_id")
      .count("store_id as count")
      .whereIn("employee_id", employeeIds)
      .groupBy("employee_id");

    // Create lookup maps for counts
    const routeCountMap: Record<string, number> = {};
    routeCounts.forEach((rc) => {
      routeCountMap[rc.employee_id] = parseInt(rc.count as string, 10);
    });

    const storeCountMap: Record<string, number> = {};
    storeCounts.forEach((sc) => {
      storeCountMap[sc.employee_id] = parseInt(sc.count as string, 10);
    });

    // Add counts to employees
    employees.forEach((employee) => {
      console.log("storeCountMap", storeCountMap);

      employee.routeCount = routeCountMap[employee.id] || 0;
      employee.storeCount = storeCountMap[employee.id] || 0;
    });
  }

  return { employees, totalCount };
};

/**
 * Get a single employee with full store and route details
 */
export const getEmployeeWithDetails = async (
  employeeId: string,
  supplierId: string
): Promise<Employee | null> => {
  // Get basic employee data with roles
  const employee = await knex
    .select(
      "employees.*",
      knex.raw(
        `COALESCE(
          json_agg(
            DISTINCT jsonb_build_object(
              'id', roles.id,
              'name', roles.name,
              'description', roles.description
            )
          ) FILTER (WHERE roles.id IS NOT NULL),
          '[]'
        ) as roles`
      )
    )
    .from("employees")
    .leftJoin("role_assignment", "employees.id", "role_assignment.employee_id")
    .leftJoin("roles", "role_assignment.role_id", "roles.id")
    .where("employees.id", employeeId)
    .where("employees.supplier_id", supplierId)
    .groupBy("employees.id")
    .first();

  if (!employee) return null;

  // Get full route details
  const routesData = await knex
    .select(
      "route.id",
      "route.name",
      "route.color",
      "route.day_of_week",
      "route.driver",
      "route.supplier_id",
      "route.config"
    )
    .from("route_assignment")
    .join("route", "route_assignment.route_id", "route.id")
    .where("route_assignment.employee_id", employeeId);

  // Get ALL store IDs assigned to this employee
  const allStoreIdsResult = await knex("store_assignment")
    .select("store_id")
    .where("employee_id", employeeId)
    .orderBy("store_id");

  const allStoreIds = allStoreIdsResult.map((row) => row.store_id);
  const storeCount = allStoreIds.length;

  // Get limited store details for display (first 100)
  const storesData = await knex
    .select("attain_user.id", "attain_user.name", "attain_user.address")
    .from("attain_user")
    .whereIn("attain_user.id", allStoreIds.slice(0, 100))
    .where("attain_user.archived", false)
    .orderBy("attain_user.name");

  return {
    id: employee.id,
    name: employee.name,
    phone: employee.phone,
    email: employee.email,
    app_access: employee.app_access,
    dashboard_access: employee.dashboard_access,
    created_at: employee.created_at,
    updated_at: employee.updated_at,
    last_login: employee.last_login,
    archived: employee.archived,
    roles: employee.roles,
    routes: routesData,
    stores: storesData,
    routeCount: routesData.length,
    storeCount: storeCount,
    allStoreIds: allStoreIds,
  };
};

export default getEmployees;
