"use strict";

var dbm;
var type;
var seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

const tableName = "store_assignment";

exports.up = function (db) {
  return db
    .createTable(tableName, {
      id: { type: "serial", primaryKey: true },
      employee_id: { type: "int", notNull: true },
      store_id: { type: "int", notNull: true },
      created_at: {
        type: "timestamp with time zone",
        defaultValue: new String("NOW()"),
      },
      updated_at: {
        type: "timestamp with time zone",
        defaultValue: new String("NOW()"),
      },
    })
    .then(() =>
      db.runSql(`
      ALTER TABLE ${tableName}
        ADD CONSTRAINT fk_store_assignment_employee
          FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
        ADD CONSTRAINT fk_store_assignment_store
          FOREIGN KEY (store_id) REFERENCES attain_user(id) ON DELETE CASCADE,
        ADD CONSTRAINT uniq_employee_store UNIQUE (employee_id, store_id);
    `)
    )
    .then(() =>
      db.runSql(`
      CREATE INDEX idx_store_assignment_employee_id ON ${tableName}(employee_id);
      CREATE INDEX idx_store_assignment_store_id ON ${tableName}(store_id);
    `)
    )
    .then(() =>
      db.runSql(`
      CREATE OR REPLACE FUNCTION update_store_assignment_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `)
    )
    .then(() =>
      db.runSql(`
      CREATE TRIGGER trigger_update_store_assignment_updated_at
      BEFORE UPDATE ON ${tableName}
      FOR EACH ROW
      EXECUTE FUNCTION update_store_assignment_updated_at();
    `)
    );
};

exports.down = function (db) {
  return db.runSql(`
    DROP TRIGGER IF EXISTS trigger_update_store_assignment_updated_at ON ${tableName};
    DROP FUNCTION IF EXISTS update_store_assignment_updated_at;
    DROP TABLE IF EXISTS ${tableName};
  `);
};

exports._meta = {
  version: 1,
};
