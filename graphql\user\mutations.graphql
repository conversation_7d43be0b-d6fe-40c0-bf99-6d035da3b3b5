type Mutation {
  # Password Reset Flow
  requestPasswordResetOtp(username: String!, channel: OtpChannel!): Boolean!
  verifyPasswordResetOtp(username: String!, code: String!): String! # Returns reset token
  resetPassword(token: String!, password: String!, confirmPassword: String!): Boolean!

  # Forgot Username Flow
  requestForgotUsernameOtp(phoneNumber: String!, channel: OtpChannel!): Boolean!
  verifyForgotUsernameOtp(phoneNumber: String!, code: String!): String! # Returns username
}
