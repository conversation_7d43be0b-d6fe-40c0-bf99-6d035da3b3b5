"use strict";

var dbm;
var type;
var seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db) {
  return db.addColumn("attain_user", "owner_name", {
    type: "string",
    length: 255,
    default: null,
  });
};

exports.down = function (db) {
  return db.removeColumn("attain_user", "owner_name");
};

exports._meta = {
  version: 1,
};
