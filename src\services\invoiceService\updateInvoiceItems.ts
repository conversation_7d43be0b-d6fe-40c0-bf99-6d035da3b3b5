import { <PERSON><PERSON> } from "knex";
import knex from "../../../knex/knex";
import { InvoiceItem, InvoiceItemInput } from "../../generated/graphql";
import { getQbIds } from "../integrationService/quickbooksService";
const updateInvoiceItems = async (
  invoiceId: string | undefined | null,
  items: InvoiceItemInput[],
  isReturning = false,
  trxProvider?: Knex.TransactionProvider | undefined | null
) => {
  try {
    // Filter out delivery fee items for QB ID lookup
    const regularItems = items.filter((item) => item.item_id !== null);
    const qb_ids =
      regularItems.length > 0
        ? await getQbIds(regularItems.map((item) => item.item_id))
        : new Map();

    const sanitizedItems = items.map((item) => {
      const { upc3, upc4, ...rest } = item;
      return {
        ...rest,
        qb_id: item.item_id
          ? item.qb_id ?? qb_ids.get(item.item_id.toString()) ?? null
          : null, // No QB ID for delivery fees
      };
    });

    const itemsToUpdate = invoiceId
      ? sanitizedItems.map((item) => ({ ...item, invoice_id: invoiceId }))
      : sanitizedItems;
    const trx = trxProvider ? await trxProvider() : await knex.transaction();

    const invoiceItems: InvoiceItem[] = await Promise.all(
      itemsToUpdate.map(async (item) => {
        const { id, ...itemData } = item;
        return trx("invoice_item")
          .update(itemData)
          .where("id", id)
          .returning(isReturning ? "*" : null);
      })
    )
      .then((results) => {
        !trxProvider && trx.commit();
        return results.map((result) => result[0]).filter((result) => result);
      })
      .catch((err) => {
        !trxProvider && trx.rollback();
        throw err;
      });
    return invoiceItems;
  } catch (err) {
    console.error("Error updating invoiceItems:", err);
    throw new Error(`Error updating Invoice Items: ${err.message}`);
  }
};

export default updateInvoiceItems;
