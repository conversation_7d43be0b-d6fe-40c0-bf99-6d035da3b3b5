"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db, callback) {
  db.addColumn(
    "item",
    "min_sale_price_percentage",
    {
      type: "decimal",
      precision: 5,
      scale: 2,
    },
    callback
  );
};

exports.down = function (db, callback) {
  db.removeColumn("item", "min_sale_price_percentage", callback);
};

exports._meta = {
  version: 1,
};
