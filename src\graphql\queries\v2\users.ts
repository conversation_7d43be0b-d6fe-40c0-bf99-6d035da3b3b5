import { QueryUsersV2Args, Ordering } from "../../../generated/graphql";
import { getUsers } from "../../../services/userService";
import { populateDeliveryWindows } from "../../../services/userService/userService";

const UsersV2 = async (_, args: QueryUsersV2Args) => {
  const {
    supplierId,
    filters,
    sortBy: { field: sortField, ordering: sortOrdering } = {
      field: "id",
      ordering: Ordering.Asc,
    },
    pagination: { offset, limit } = { offset: 0, limit: 100 },
    includeCustomPrices = true,
  } = args.usersInput;

  const result = await getUsers({
    supplierId,
    filters,
    sortBy: { field: sortField, ordering: sortOrdering },
    pagination: { offset, limit },
    includeCustomPrices,
    includeSuppliers: true,
  });

  await populateDeliveryWindows(result.users);

  return result;
};
export default UsersV2;
