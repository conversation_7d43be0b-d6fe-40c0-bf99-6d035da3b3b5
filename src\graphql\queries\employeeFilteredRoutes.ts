// src/graphql/queries/employeeFilteredRoutes.ts - Fixed with proper error handling

import { QueryResolvers } from "../../generated/graphql";
import { getEmployeeFilteredRoutes } from "../../services/accessService/employeeFilteringService";

const employeeFilteredRoutes: QueryResolvers["employeeFilteredRoutes"] = async (
  _,
  { employeeId, supplierId }
) => {
  try {
    // Validate required parameters
    if (!employeeId || !supplierId) {
      console.error("Missing required parameters:", { employeeId, supplierId });
      return {
        routes: [],
        hasAllAccess: false,
      };
    }

    const result = await getEmployeeFilteredRoutes(employeeId, supplierId);
    // Ensure we always return a valid structure
    return {
      routes: result.routes || [],
      hasAllAccess: result.hasAllAccess || false,
    };
  } catch (error) {
    console.error("Error in employeeFilteredRoutes resolver:", error);

    // NEVER throw an error - always return a valid response for non-nullable fields
    return {
      routes: [],
      hasAllAccess: false,
    };
  }
};

export default employeeFilteredRoutes;
