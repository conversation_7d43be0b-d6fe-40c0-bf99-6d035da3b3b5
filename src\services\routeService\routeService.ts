import dayjs from "dayjs";
import knex from "../../../knex/knex";
import { Route } from "../../generated/graphql";

export const getRoutes = async (
  supplierId: string,
  filters?: { routeId?: string }
): Promise<Route[]> => {
  let query = knex("route")
    .select(
      "route.id",
      "route.supplier_id",
      "route.name",
      "route.color",
      "route.day_of_week",
      "route.order_day",
      "route.driver",
      "route.config"
    )
    .where("route.supplier_id", supplierId);

  if (filters?.routeId) {
    query = query.where("route.id", filters.routeId);
  }

  const routes = await query;

  const routesWithStoreCounts = await Promise.all(
    routes.map(async (route) => {
      const storeCountResult = await knex("attain_user")
        .count("* as count")
        .where(function () {
          // Check if route ID is in the comma-separated route_id field
          this.whereRaw("? = ANY(string_to_array(route_id, ','))", [
            route.id.toString(),
          ])
            // Or check if it's the primary route
            .orWhere("primary_route_id", route.id);
        })
        .where("archived", false) // Only count active stores
        .first();

      const storeCount = parseInt(storeCountResult?.count as string) || 0;

      return {
        ...route,
        store_count: storeCount,
      };
    })
  );

  return routesWithStoreCounts;
};

export const getRoutesOnDay = async (
  supplierId: string,
  date: Date
): Promise<Route[]> => {
  // Convert Date to day name (e.g., "Monday", "Tuesday", etc.)
  const dayName = dayjs.utc(date).format("dddd");

  const routes = await getRoutes(supplierId);
  // Filter out UNASSIGNED routes and match by day
  return routes.filter(
    (route) => route.day_of_week === dayName && route.name !== "UNASSIGNED"
  );
};
