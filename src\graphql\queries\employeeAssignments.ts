// src/graphql/queries/employeeAssignments.ts - Fixed with proper error handling

import { QueryResolvers } from "../../generated/graphql";
import { getEmployeeAssignments } from "../../services/storeAssignmentService";

const employeeAssignments: QueryResolvers["employeeAssignments"] = async (
  _,
  { employeeId }
) => {
  try {
    // Validate required parameters
    if (!employeeId) {
      console.error("Missing employeeId parameter");
      return {
        routeIds: [],
        storeIds: [],
        hasAllAccess: false,
      };
    }

    const result = await getEmployeeAssignments(employeeId);

    // Ensure we always return a valid structure
    return {
      routeIds: result.routeIds || [],
      storeIds: result.storeIds || [],
      hasAllAccess: result.hasAllAccess || false,
    };
  } catch (error) {
    console.error("Error in employeeAssignments resolver:", error);

    // NEVER throw an error - always return a valid response for non-nullable fields
    return {
      routeIds: [],
      storeIds: [],
      hasAllAccess: false,
    };
  }
};

export default employeeAssignments;
