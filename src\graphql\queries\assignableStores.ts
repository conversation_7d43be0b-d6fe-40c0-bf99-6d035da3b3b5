// src/graphql/queries/assignableStores.ts

import { getAssignableStores } from "../../services/storeAssignmentService";

const assignableStores = async (_, { input }) => {
  try {
    const result = await getAssignableStores({
      filters: input.filters,
      pagination: input.pagination,
      sortBy: input.sortBy,
    });

    return {
      stores: result.stores,
      totalCount: result.totalCount,
    };
  } catch (error) {
    console.error("Error in assignableStores query:", error);
    throw new Error("Failed to fetch assignable stores");
  }
};

export default assignableStores;
