// src/graphql/mutations/assignStoresToEmployee.ts

import { assignStoresToEmployee } from "../../services/storeAssignmentService";

const assignStoresToEmployeeResolver = async (_, { input }) => {
  const { employeeId, storeIds } = input;

  try {
    await assignStoresToEmployee(employeeId, storeIds);
    return true;
  } catch (error) {
    console.error("Error in assignStoresToEmployee mutation:", error);
    throw new Error("Failed to assign stores to employee");
  }
};

export default assignStoresToEmployeeResolver;
