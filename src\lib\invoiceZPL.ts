import { Invoice, InvoiceItem, Route, Supplier } from "../generated/graphql";
import { USDollarFromNumber } from "../util/formats";
import { PNG } from "pngjs";
import { rgbaToZ64 } from "zpl-image";
import ZPL from "./zpl";

export type TableColumnWidths = {
  [key: string]: {
    title: string;
    width: number;
    pad: "right" | "left" | "center";
    format: "currency" | "string";
  };
};

export type TotalsRows = {
  [key: string]: {
    title: string;
  };
};

const splitWithWordWrap = (s: string, maxWidth: number) => {
  const words = s.split(" ");
  let currentLine = "";
  const splitParts = [];

  words.forEach((word) => {
    if ((currentLine + " " + word).trim().length <= maxWidth) {
      currentLine = (currentLine + " " + word).trim();
    } else {
      if (currentLine) splitParts.push(currentLine);
      currentLine = word;
    }
  });
  if (currentLine) splitParts.push(currentLine);

  return splitParts;
};

const generateInvoiceHeader = (
  invoice: Invoice,
  supplier: Supplier,
  routes: Route[],
  zpl: ZPL,
  tableMaxWidth: number = 60,
  scaleWidth: number = 1
) => {
  zpl.changeDefaultFontAndSize("A", 40);
  zpl.text(
    `${invoice.subtotal >= 0 ? "Invoice" : "Credit Memo"}: #${invoice.id}`,
    40
  );
  zpl.changeDefaultFontAndSize("A", 20);
  // zpl.text(
  //   `${invoice.subtotal >= 0 ? "Order" : "Credit"}: #${invoice.order_id ?? ""}`
  // );
  zpl.text(`Date: ${new Date(invoice.date_created).toLocaleString()}`);
  supplier.id !== "31" &&
    zpl.text(
      `Route${routes.length > 1 ? "s" : ""}: ${
        routes.length ? routes.map((r) => r.name).join(", ") : "N/A"
      }`
    );
  // zpl.text(`Driver Name: `);
  // zpl.text(`Salesperson: `);
  zpl.changeDefaultFontAndSize("A", 40);
  zpl.text(`${supplier.name}`, 40, undefined, 20);
  zpl.changeDefaultFontAndSize("A", 20);
  (supplier.address || "")
    .replaceAll(", ", "\n")
    .split("\n")
    .forEach((line) => zpl.text(line));
  // zpl.text(`Address Line 1`);
  // zpl.text(`Address Line 2`);
  // zpl.text(`City, ST 12345`);
  zpl.text(`Phone: ${supplier.phone_number}`);
  supplier.email && zpl.text(`Email: ${supplier.email}`);
  // zpl.text(`Email: <EMAIL>`);

  zpl.changeDefaultFontAndSize("A", 40);
  const maxNameLength = Math.round((tableMaxWidth * scaleWidth) / 2);
  if (invoice.customerDetails.name.length > maxNameLength) {
    const splitParts = splitWithWordWrap(
      invoice.customerDetails.name.toUpperCase(),
      maxNameLength
    );
    zpl.text(splitParts[0], 40, undefined, 20);
    splitParts.slice(1).forEach((textContent) => {
      zpl.text(textContent, 40, undefined);
    });
  } else {
    zpl.text(
      `${invoice.customerDetails.name.toUpperCase()}`,
      40,
      undefined,
      20
    );
  }
  zpl.changeDefaultFontAndSize("A", 20);
  (invoice.customerDetails.address || "")
    .replaceAll(", ", "\n")
    .split("\n")
    .forEach((line) => zpl.text(line));
  // zpl.text(`Address Line 1`);
  // zpl.text(`Address Line 2`);
  // zpl.text(`City, ST 12345`);
  zpl.text(`Phone: ${invoice.customerDetails.phone_number}`);

  zpl.text(
    `FINAL ${invoice.subtotal >= 0 ? "INVOICE" : "CREDIT"}`,
    40,
    "D,40",
    20
  );
};

const generateTableRow = (
  tableColumnWidths: TableColumnWidths,
  data?: Record<string, any>,
  scaleWidth: number = 1
) => {
  return Object.keys(tableColumnWidths)
    .map((key) => {
      const currentTableColumnWidth = tableColumnWidths[key];
      let textContent = data ? data[key] : currentTableColumnWidth.title;
      if (data && typeof textContent !== "string") {
        if (currentTableColumnWidth.format === "currency") {
          textContent = USDollarFromNumber(textContent);
        } else {
          textContent = textContent ? textContent.toString() : "";
        }
      }
      if (
        textContent.length <
        Math.round(currentTableColumnWidth.width * scaleWidth)
      ) {
        if (currentTableColumnWidth.pad === "left") {
          textContent = textContent.padStart(
            Math.round(currentTableColumnWidth.width * scaleWidth),
            " "
          );
        } else if (currentTableColumnWidth.pad === "right") {
          textContent = textContent.padEnd(
            Math.round(currentTableColumnWidth.width * scaleWidth),
            " "
          );
        } else {
          const paddingLeft =
            (Math.round(currentTableColumnWidth.width * scaleWidth) -
              textContent.length) /
            2;
          const paddingRight =
            Math.round(currentTableColumnWidth.width * scaleWidth) -
            textContent.length -
            paddingLeft;
          textContent = textContent.padStart(paddingLeft).padEnd(paddingRight);
        }
      } else {
        textContent = textContent.substring(
          0,
          Math.round(currentTableColumnWidth.width * scaleWidth)
        );
      }
      return textContent.toUpperCase();
    })
    .join("");
};

const generateTable = (
  invoice: Invoice,
  zpl: ZPL,
  tableColumnWidths: TableColumnWidths,
  tableMaxWidth = 60,
  supplier: Supplier,
  hideBarcodes?: boolean,
  scaleWidth: number = 1
) => {
  const totalsRow = {
    name: "Totals:".padStart(
      Math.round(tableColumnWidths.name.width * scaleWidth),
      " "
    ),
    quantity: invoice.invoiceItems
      .map((i) => {
        if (i.item_uom_id && i.uoms) {
          const uoms = Array.isArray(i.uoms) ? i.uoms : [i.uoms];
          const uom = uoms.find((u) => u.id === i.item_uom_id);
          if (uom) {
            return i.quantity / uom.quantity;
          }
        }
        return i.quantity;
      })
      .reduce((total, q) => total + q, 0),
    price: "",
    total:
      invoice.invoiceItems
        .map((i) => i.quantity * i.price)
        .reduce((total, p) => total + p, 0) -
      (invoice.discount || 0) -
      (invoice.credit || 0),
  };

  zpl.changeDefaultFontAndSize("A", 20);
  zpl.text("-".repeat(tableMaxWidth * scaleWidth));
  zpl.text(generateTableRow(tableColumnWidths, undefined, scaleWidth));
  zpl.text("-".repeat(tableMaxWidth * scaleWidth));

  if (supplier.id === "68") {
    invoice.invoiceItems
      .sort((a, b) => {
        const aMetadata = a?.metadata || "";
        const bMetadata = b?.metadata || "";
        return aMetadata.localeCompare(bMetadata);
      })
      .map((item) => {
        let itemName = item.name;
        let displayQuantity = item.quantity;
        let displayPrice = item.price;
        let displayTotal = item.quantity * item.price;

        // Add UOM info if available and adjust quantity, price, and total
        if (item.item_uom_id && item.uoms) {
          const uoms = Array.isArray(item.uoms) ? item.uoms : [item.uoms];
          const uom = uoms.find((u) => u.id === item.item_uom_id);
          if (uom) {
            itemName += ` UOM: ${uom.name} (${uom.quantity})`;
            displayQuantity = item.quantity / uom.quantity;
            displayPrice = item.price * uom.quantity;
            displayTotal = displayQuantity * displayPrice;
          }
        }

        return {
          name: itemName,
          upc: item.upc1,
          quantity: displayQuantity,
          price: displayPrice,
          total: displayTotal,
          unit_size: item.unit_size,
        };
      })
      .forEach((row) => {
        const maxNameLength = Math.round(
          tableColumnWidths.name.width * scaleWidth
        );
        if (row.name.length > maxNameLength) {
          const splitParts = splitWithWordWrap(
            row.name.toUpperCase(),
            maxNameLength
          );
          zpl.text(
            generateTableRow(
              tableColumnWidths,
              { ...row, name: splitParts[0] },
              scaleWidth
            ),
            25,
            undefined,
            10
          );
          splitParts.slice(1).forEach((textContent) => {
            zpl.text(textContent);
          });
        } else {
          zpl.text(
            generateTableRow(tableColumnWidths, row, scaleWidth),
            25,
            undefined,
            10
          );
        }
        if (!hideBarcodes) zpl.barcode(row.upc, 5);
      });
  } else {
    invoice.invoiceItems
      .sort((a, b) => a.name.localeCompare(b.name))
      .map((item) => {
        let itemName = item.name;
        let displayQuantity = item.quantity;
        let displayPrice = item.price;
        let displayTotal = item.quantity * item.price;

        // Add UOM info if available and adjust quantity, price, and total
        if (item.item_uom_id && item.uoms) {
          const uoms = Array.isArray(item.uoms) ? item.uoms : [item.uoms];
          const uom = uoms.find((u) => u.id === item.item_uom_id);
          if (uom) {
            itemName += ` UOM: ${uom.name} (${uom.quantity})`;
            displayQuantity = item.quantity / uom.quantity;
            displayPrice = item.price * uom.quantity;
            displayTotal = displayQuantity * displayPrice;
          }
        }

        return {
          name: itemName,
          upc: item.upc1,
          quantity: displayQuantity,
          price: displayPrice,
          total: displayTotal,
          unit_size: item.unit_size,
        };
      })
      .forEach((row) => {
        const maxNameLength = Math.round(
          tableColumnWidths.name.width * scaleWidth
        );
        if (row.name.length > maxNameLength) {
          const splitParts = splitWithWordWrap(
            row.name.toUpperCase(),
            maxNameLength
          );
          zpl.text(
            generateTableRow(
              tableColumnWidths,
              { ...row, name: splitParts[0] },
              scaleWidth
            ),
            25,
            undefined,
            10
          );
          splitParts.slice(1).forEach((textContent) => {
            zpl.text(textContent);
          });
        } else {
          zpl.text(
            generateTableRow(tableColumnWidths, row, scaleWidth),
            25,
            undefined,
            10
          );
        }
        if (!hideBarcodes) zpl.barcode(row.upc, 5);
      });
  }

  zpl.text("-".repeat(tableMaxWidth * scaleWidth));
  zpl.text(generateTableRow(tableColumnWidths, totalsRow, scaleWidth));
  zpl.text("-".repeat(tableMaxWidth * scaleWidth));
};

const generateInvoiceTotals = (
  invoice: Invoice,
  zpl: ZPL,
  totalsRows: TotalsRows,
  scaleWidth: number = 1,
  supplierId: string
) => {
  zpl.addVerticalOffset(75);
  zpl.changeDefaultFontAndSize("A", 40);
  const maxRowLeftSideWidth =
    Object.keys(totalsRows)
      .map((k) => totalsRows[k].title.length)
      .reduce((m, t) => Math.max(m, t), 0) * 1;
  const maxRowRightSideWidth = 16;

  Object.keys(totalsRows).forEach((k) => {
    zpl.text(
      [
        totalsRows[k].title.toUpperCase().padStart(maxRowLeftSideWidth, " "),
        USDollarFromNumber(
          k === "total" && supplierId === "31"
            ? invoice["total"] - invoice["paid"]
            : invoice[k] ?? 0
        ).padStart(Math.round(maxRowRightSideWidth * scaleWidth), " "),
      ].join(": "),
      45
    );
  });
};

const generateInvoiceFooter = (
  invoice: Invoice,
  zpl: ZPL,
  scaleWidth: number = 1
) => {
  zpl.addVerticalOffset(40);
  zpl.changeDefaultFontAndSize("A", 20);
  zpl.text(`Notes:`);
  zpl.text(invoice.notes ?? "");
  zpl.addVerticalOffset(40);
  const maxSignatureLineLength = 25;
  if (invoice.signature) {
    const signaturePNG = PNG.sync.read(
      Buffer.from(invoice.signature.split(",")[1], "base64")
    );
    zpl.image(rgbaToZ64(signaturePNG.data, signaturePNG.width), 40, 50);
  }
  zpl.addVerticalOffset(75);
  zpl.changeDefaultFontAndSize("A", 20);
  zpl.text("-".repeat(maxSignatureLineLength * scaleWidth), 15);
  zpl.text("Signature");
  invoice.signature_name &&
    zpl.text(`Signature Name: ${invoice.signature_name}`);
  zpl.text("", 80);
};

const generateInvoiceZPLString = (
  invoice: Invoice,
  supplier: Supplier,
  routes: Route[],
  tableColumnWidths: TableColumnWidths,
  tableMaxWidth: number,
  totalsRows: TotalsRows,
  scaleHeight: number = 1,
  scaleWidth: number = 1
) => {
  const zpl = new ZPL(80, "", scaleHeight, scaleWidth);

  generateInvoiceHeader(
    invoice,
    supplier,
    routes,
    zpl,
    tableMaxWidth,
    scaleWidth
  );
  generateTable(
    invoice,
    zpl,
    tableColumnWidths,
    tableMaxWidth,
    supplier,
    supplier.id === "31",
    scaleWidth
  );
  generateInvoiceTotals(invoice, zpl, totalsRows, scaleWidth, supplier.id);
  generateInvoiceFooter(invoice, zpl, scaleWidth);

  return zpl.getZPLContents();
};

export {
  generateInvoiceHeader,
  generateTableRow,
  generateTable,
  generateInvoiceTotals,
  generateInvoiceFooter,
  generateInvoiceZPLString,
};

export default generateInvoiceZPLString;
