import { awsS3Config, env, sentryConfig } from "./src/config/environment";
import * as Sentry from "@sentry/node";
import { nodeProfilingIntegration } from "@sentry/profiling-node";

// Wrap Sentry initialization in a production check
const initSentry = () => {
  if (env.production) {
    Sentry.init({
      dsn: sentryConfig.dsn,
      // debug: true,
      integrations: [
        // Add our Profiling integration
        nodeProfilingIntegration(),
        Sentry.consoleLoggingIntegration({ levels: ["error", "warn"] }),
      ],
      // Reduce sample rate in production for performance
      tracesSampleRate: 0.2, // Sample 20% of requests
    });
    return true;
  }
  return false;
};

// Initialize Sentry and add middleware only in production
const sentryEnabled = initSentry();

import {
  ApolloServer,
  ApolloServerPlugin,
  GraphQLRequestContext,
  BaseContext,
} from "@apollo/server";
import { expressMiddleware } from "@apollo/server/express4";
import { ApolloServerPluginDrainHttpServer } from "@apollo/server/plugin/drainHttpServer";
import { ApolloServerPluginInlineTrace } from "@apollo/server/plugin/inlineTrace";
import { S3Client } from "@aws-sdk/client-s3";
import bodyParser from "body-parser";
import cors from "cors";
import express from "express";
import { readFileSync } from "fs";
import http from "http";
import multer from "multer";
import { AddressInfo, ListenOptions } from "net";
import addInvoice from "./src/apis/addInvoice";
import createAccount from "./src/apis/createAccount";
import healthCheck from "./src/apis/healthCheck";
import orderSubmitted from "./src/apis/orderSubmitted";
import sendInvoice from "./src/apis/sendInvoice";
import authQuickBooks from "./src/apis/authQuickBooks";
import disconnectQuickBooks from "./src/apis/disconnectQuickBooks";
import updateQuickBooksData from "./src/apis/updateQuickBooksData";
import generateInvoiceZPL from "./src/apis/generateInvoiceZPL";
import generateCatalog from "./src/apis/generateCatalog";
import sendCatalog from "./src/apis/sendCatalog";
import resolvers from "./src/graphql";
import Expo from "expo-server-sdk";
import sgMail from "@sendgrid/mail";
import OAuthClient, { Environment } from "intuit-oauth-ts";
import SimpleCrypto from "simple-crypto-js";
import addressAutocomplete from "./src/apis/addressAutocomplete";
import uploadImage from "./src/apis/uploadImage";
import DailyBackInStockNotification from "./src/apis/dailyBackInStockNotification";

const customLoggingPlugin: ApolloServerPlugin = {
  // Fires whenever a GraphQL request is received from a client.
  async requestDidStart(requestContext: GraphQLRequestContext<BaseContext>) {
    if (requestContext.request.operationName === "IntrospectionQuery") return;
    console.log(
      "[ApolloServer] Request started for Query: " +
        requestContext.request.operationName
    );

    return {
      // Fires whenever Apollo Server will parse a GraphQL
      // request to create its associated document AST.
      async parsingDidStart(
        requestContext: GraphQLRequestContext<BaseContext>
      ) {
        console.log(
          "[ApolloServer] Parsing started for Query: " +
            requestContext.request.operationName
        );
      },

      // Fires whenever Apollo Server will validate a
      // request's document AST against your GraphQL schema.
      async validationDidStart(
        requestContext: GraphQLRequestContext<BaseContext>
      ) {
        console.log(
          "[ApolloServer] Validation started for Query: " +
            requestContext.request.operationName
        );
      },

      async willSendResponse(
        requestContext: GraphQLRequestContext<BaseContext>
      ) {
        console.log(
          "[ApolloServer] Response sent for Query: " +
            requestContext.request.operationName
        );
      },

      async didEncounterErrors(ctx) {
        console.error("[ApolloServer] Encountered errors:", {
          operation: ctx.operation?.operation || "unknown operation",
          operationName: ctx.operationName || "unnamed operation",
          errors: ctx.errors.map((error) => ({
            message: error.message,
            path: error.path,
            extensions: error.extensions,
            stack: error.stack,
          })),
        });

        for (const err of ctx.errors) {
          Sentry.captureException(err, {
            extra: {
              operationName: ctx.operationName,
              query: ctx.request.query,
              variables: ctx.request.variables,
            },
          });
        }
      },
    };
  },
};

// This function will create a new server with an Apollo Server instance
export const createServer = async (options: ListenOptions = { port: 0 }) => {
  // Initialize Express Server
  const app = express();
  // Initialize HTTP Server
  const httpServer = http.createServer(app);

  // Configure ApolloServer
  const typeDefs = readFileSync("./schema.graphql").toString("utf-8");
  const apolloServer = new ApolloServer({
    typeDefs,
    resolvers,
    introspection: true,
    plugins: [
      ApolloServerPluginDrainHttpServer({ httpServer }),
      // ApolloServerPluginInlineTrace({ includeErrors: { masked: true } }),
      customLoggingPlugin,
    ],
  });
  const apiEndpointPrefix = "/api";
  // Wait for ApolloServer to start
  await apolloServer.start();
  //TODO: enable push security and add access token.
  const expoClient = new Expo();

  // Configure Express Server
  // Increase body size limits for endpoints
  app.use(bodyParser.json({ limit: "10mb" }));
  app.use(bodyParser.urlencoded({ extended: true, limit: "10mb" }));
  app.use(express.json());

  // Configure CORS
  const allowedOrigins = [
    // Production web domains
    "https://app.joinattain.com",
    "https://staging.joinattain.com",
    // Local development
    "http://localhost:3000", // Local web development
    "http://localhost:19006", // Expo React Native web
    "http://localhost:19000", // Expo React Native dev
    "exp://127.0.0.1:19000", // Expo Go app
    "exp://localhost:19000", // Expo alternative local
    "exp://***********:19000", // Expo on local network
  ].filter(Boolean); // Remove any undefined values

  const corsOptions = {
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        console.warn(`Blocked request from unauthorized origin: ${origin}`);
        callback(new Error("Not allowed by CORS"));
      }
    },
    credentials: true, // Allow credentials (cookies, authorization headers)
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
  };

  // Apply CORS globally for all routes
  app.use(cors(corsOptions));

  // Configure AWS S3
  const s3Client = new S3Client({
    credentials: awsS3Config.credentials,
    region: awsS3Config.region,
  });

  // Configure multer
  const upload = multer({
    storage: multer.memoryStorage(),
  });

  // Configure Sendgrid
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);

  // Configure Intuit OAuth2Client for QuickBooks
  const oAuthConfig = env.production
    ? {
        clientId: process.env.QUICKBOOKS_CLIENT_ID,
        clientSecret: process.env.QUICKBOOKS_CLIENT_SECRET,
        environment: "production" as keyof Environment,
        redirectUri: `https://attain-server.herokuapp.com/api/authQuickBooks/`,
      }
    : {
        clientId: process.env.QUICKBOOKS_CLIENT_ID_SANDBOX,
        clientSecret: process.env.QUICKBOOKS_CLIENT_SECRET_SANDBOX,
        environment: "sandbox" as keyof Environment,
        redirectUri: `${process.env.SELF_URL}/api/authQuickBooks/${process.env.NODE_ENV}`,
      };
  const intuitOAuthClient = new OAuthClient(oAuthConfig);

  // Configure SimpleCrypto
  const simpleCrypto = new SimpleCrypto(oAuthConfig.clientId);

  // Declare all endpoints
  app.get(
    `${apiEndpointPrefix}/healthCheck`,
    new healthCheck(apolloServer, expoClient).handler
  );
  app.post(
    `${apiEndpointPrefix}/createAccount`,
    new createAccount(apolloServer, expoClient).handler
  );
  app.post(
    `${apiEndpointPrefix}/addInvoice`,
    new addInvoice(apolloServer, expoClient).handler
  );
  app.post(
    `${apiEndpointPrefix}/orderSubmitted`,
    new orderSubmitted(apolloServer, expoClient).handler
  );
  app.post(
    `${apiEndpointPrefix}/sendInvoice`,
    upload.any(),
    new sendInvoice(apolloServer, expoClient, s3Client, sgMail).handler
  );
  app.post(
    `${apiEndpointPrefix}/generateCatalog`,
    new generateCatalog(apolloServer, expoClient).handler
  );
  app.post(
    `${apiEndpointPrefix}/sendCatalog`,
    new sendCatalog(apolloServer, expoClient, sgMail).handler
  );
  app.get(
    `${apiEndpointPrefix}/authQuickBooks/:env?`,
    new authQuickBooks(
      apolloServer,
      expoClient,
      intuitOAuthClient,
      simpleCrypto
    ).handler
  );
  app.get(
    `${apiEndpointPrefix}/disconnectQuickBooks/:env?`,
    new disconnectQuickBooks(apolloServer, expoClient, simpleCrypto).handler
  );
  app.post(
    `${apiEndpointPrefix}/updateQuickBooksData`,
    new updateQuickBooksData(apolloServer, expoClient).handler
  );
  app.get(
    `${apiEndpointPrefix}/generateInvoiceZPL`,
    new generateInvoiceZPL(apolloServer, expoClient).handler
  );
  app.get(
    `${apiEndpointPrefix}/addressAutocomplete`,
    new addressAutocomplete(apolloServer, expoClient).handler
  );
  app.post(
    `${apiEndpointPrefix}/uploadImage`,
    upload.single("image"),
    new uploadImage(apolloServer, expoClient, s3Client).handler
  );

  app.get(
    `${apiEndpointPrefix}/dailyBackInStockNotification`,
    new DailyBackInStockNotification(apolloServer, expoClient).handler
  );

  // Specify mount endpoint for ApolloServer
  app.use("/", bodyParser.json(), expressMiddleware(apolloServer));

  if (sentryEnabled) {
    Sentry.setupExpressErrorHandler(app);
  }

  // Start up HTTP Server to listen for requests
  let server: http.Server;
  await new Promise<void>(
    (resolve) => (server = httpServer.listen(options, resolve))
  );

  // Retrieve and print server info
  const addressInfo = server.address() as AddressInfo;
  const serverUrl = `http://${
    addressInfo.family === "IPv6"
      ? "[" + addressInfo.address + "]"
      : addressInfo.address
  }:${addressInfo.port}`;
  if (process.env.NODE_ENV !== "test") {
    console.log(`Server listening at ${serverUrl}`);
  }

  // Return ApolloServer, HTTPServer, and HTTPServer's listen URL
  return { apolloServer, server, serverUrl };
};
