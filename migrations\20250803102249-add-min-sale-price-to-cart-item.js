"use strict";

var dbm;
var type;
var seed;

/**
 * We receive the dbmigrate dependency from dbmigrate initially.
 * This enables us to not have to rely on NODE_PATH.
 */
exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

exports.up = function (db, callback) {
  db.addColumn(
    "cart_item",
    "min_sale_price",
    {
      type: "decimal",
      precision: 10,
      scale: 2,
    },
    callback
  );
};

exports.down = function (db, callback) {
  db.removeColumn("cart_item", "min_sale_price", callback);
};

exports._meta = {
  version: 1,
};
