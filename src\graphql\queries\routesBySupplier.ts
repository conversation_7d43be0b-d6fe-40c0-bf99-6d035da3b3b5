import knex from "../../../knex/knex";
import { QueryRoutesBySupplierArgs } from "../../generated/graphql";

const RoutesBySupplier = async (_, args: QueryRoutesBySupplierArgs) => {
  const {
    supplierId,
    includeStoreCount = false,
    includeEmployeeData = false,
  } = args.getRoutesBySupplierInput;

  // Get basic route data
  const routes = await knex("route")
    .where("supplier_id", supplierId)
    .orderBy("name", "asc");

  // If no additional data is requested, return basic routes
  if (!includeStoreCount && !includeEmployeeData) {
    return routes;
  }

  // Calculate additional data only when requested
  const routesWithData = await Promise.all(
    routes.map(async (route) => {
      let store_count = undefined;
      let assignedDriver = route.driver;

      if (includeStoreCount) {
        const storeCountResult = await knex("attain_user as u")
          .innerJoin("supplier_times as st", "u.id", "st.business_id")
          .count("* as count")
          .where("st.supplier_id", supplierId)
          .where("u.supplier_beta", true)
          .whereNot("u.archived", true)
          .where(function () {
            this.where("u.route_id", route.id.toString())
              .orWhereRaw("? = ANY(string_to_array(u.route_id, ','))", [
                route.id.toString(),
              ])
              .orWhere("u.primary_route_id", route.id);
          })
          .first();

        store_count = parseInt(storeCountResult?.count as string) || 0;
      }

      if (includeEmployeeData) {
        const assignedEmployees = await knex("route_assignment as ra")
          .select("e.id", "e.name", "e.email")
          .join("employees as e", "ra.employee_id", "e.id")
          .where("ra.route_id", route.id)
          .where("e.archived", false);

        // Use the first assigned employee's name, or fall back to existing driver field
        if (assignedEmployees.length > 0) {
          assignedDriver = assignedEmployees[0].name;
        }
      }

      return {
        ...route,
        ...(includeStoreCount && { store_count }),
        driver: assignedDriver,
      };
    })
  );

  return routesWithData;
};

export default RoutesBySupplier;
