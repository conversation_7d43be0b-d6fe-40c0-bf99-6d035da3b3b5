{"name": "attain-server", "version": "1.0.0", "main": "dist/app.js", "license": "MIT", "dependencies": {"@apollo/server": "^4.9.1", "@aws-sdk/client-s3": "^3.387.0", "@aws-sdk/lib-storage": "^3.387.0", "@sendgrid/mail": "^7.7.0", "@sentry/node": "^9.40.0", "@sentry/profiling-node": "^9.40.0", "@types/multer": "^1.4.7", "algoliasearch": "^4.22.1", "auth0": "^4.22.0", "axios": "^0.27.2", "body-parser": "^1.20.2", "conductor-node": "^11.6.15", "csv-parse": "^5.6.0", "csvtojson": "^2.0.10", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dayjs": "^1.11.13", "db-migrate": "^0.11.13", "db-migrate-pg": "^1.2.2", "dotenv": "^16.3.1", "expo-server-sdk": "^3.7.0", "exponential-backoff": "^3.1.1", "firebase": "^9.19.1", "firebase-admin": "^13.2.0", "graphql": "^16.3.0", "graphql-iso-date": "^3.6.1", "graphql-type-json": "^0.3.2", "intuit-oauth": "^4.1.2", "intuit-oauth-ts": "^0.0.4", "jspdf": "^3.0.1", "jszpl": "^1.1.8", "knex": "^2.3.0", "lodash": "^4.17.21", "mathjs": "^11.9.1", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "node-quickbooks": "^2.0.45", "node-schedule": "^2.1.1", "pngjs": "^7.0.0", "react-instantsearch-native": "^6.23.0", "sharp": "^0.34.3", "simple-crypto-js": "^3.0.1", "ts-node-dev": "^2.0.0", "twilio": "^5.8.0", "uuid": "^9.0.0", "zpl-image": "^0.2.0"}, "devDependencies": {"@graphql-codegen/cli": "2.6.2", "@graphql-codegen/typescript": "2.4.5", "@graphql-codegen/typescript-resolvers": "2.5.2", "@types/cors": "^2.8.13", "@types/db-migrate-pg": "^0.0.10", "@types/dotenv": "^8.2.0", "@types/jest": "^29.5.2", "@types/mime-types": "^2.1.1", "@types/node-schedule": "^2.1.0", "@types/pngjs": "^6.0.5", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.2", "@types/zpl-image": "^0.1.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "concurrently": "^9.1.2", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "firebase": "^9.19.1", "jest": "^29.5.0", "prettier": "^2.8.0", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "typescript": "^5.1.6"}, "scripts": {"build": "tsc && cp -r src/lib/fonts dist/src/lib/", "start": "node dist/app.js", "heroku-postbuild": "yarn codegen && yarn build", "dev": "concurrently \"ts-node-dev app.ts\" \"yarn codegen --watch\"", "dev:debug": "ts-node-dev --inspect -- app.ts", "codegen": "graphql-codegen --config codegen.yml", "migrate-prod": "db-migrate up --env production --config database.json", "migrate-dev": "db-migrate up --env development --config database.json", "migrate-test": "db-migrate up --env test --config database.json", "reset-test": "db-migrate reset --env test --config database.json", "format": "prettier --check .", "auto-format": "prettier --write .", "lint": "eslint .", "auto-lint": "eslint --fix .", "run-tests": "jest", "recreate-test-db": "./recreate-test-db.sh", "seed-test": "knex seed:run --env test", "seed-dev": "knex seed:run --env development", "test": "yarn reset-test && yarn migrate-test && yarn seed-test && yarn run-tests"}}