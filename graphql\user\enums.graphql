enum OtpChannel {
    SMS
    VOICE
    WHATSAPP
}

type Mutation {
  requestPasswordResetOtp(username: String!, channel: OtpChannel!): Boolean!
  verifyPasswordResetOtp(username: String!, code: String!): String! # Returns reset token
  resetPassword(token: String!, password: String!, confirmPassword: String!): Boolean!

  requestForgotUsernameOtp(phoneNumber: String!, channel: OtpChannel!): Boolean!
  verifyForgotUsernameOtp(phoneNumber: String!, code: String!): String! # Returns username
}

input RequestPasswordResetOtpInput {
  username: String!
  channel: OtpChannel!
}

input VerifyPasswordResetOtpInput {
  username: String!
  code: String!
}   

input RequestForgotUsernameOtpInput {
  phoneNumber: String!
  channel: OtpChannel!
}

