import knex from "../../../knex/knex";
import {
  QueryUsersByFilterArgs,
  User,
  Ordering,
} from "../../generated/graphql";
import {
  populateUsersCustomPrices,
  populateDeliveryWindows,
} from "../../services/userService/userService";
import getUsersSuppliers from "../../services/userService/getUsersSuppliers";
import { populateUsersCustomUOMPrices } from "../../services/itemService/uomService";

// Function to calculate unpaid balance for users
const calculateUnpaidBalances = async (users: User[]) => {
  const userIds = users.map((user) => user.id);

  const invoiceBalances = await knex("invoice")
    .select("user_id")
    .select(
      knex.raw(
        "sum(COALESCE(subtotal, 0) - COALESCE(paid, 0) - COALESCE(credit, 0)) as unpaid_balance"
      )
    )
    .whereIn("user_id", userIds)
    .whereNot("archived", true)
    .whereRaw(
      "ABS(COALESCE(subtotal, 0) - COALESCE(paid, 0) - COALESCE(credit, 0)) > 1e-10"
    )
    .groupBy("user_id");

  const balanceMap = new Map();
  invoiceBalances.forEach((row) => {
    balanceMap.set(row.user_id, parseFloat(row.unpaid_balance) || 0);
  });

  // Add unpaid_balance to each user
  users.forEach((user) => {
    user.unpaid_balance = balanceMap.get(user.id) || 0;
  });
};

const populateOpenCarts = async (users: User[]) => {
  const userIds = users.map((user) => user.id);
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

  const openCarts = await knex("cart as c")
    .select("c.*")
    .whereIn("c.user_id", userIds)
    .where("c.updated_at", "<", oneHourAgo)
    .where(function () {
      this.where("c.subtotal", ">", 0).orWhereExists(function () {
        this.select("*").from("cart_item").whereRaw("cart_item.cart_id = c.id");
      });
    });

  if (openCarts.length === 0) {
    return;
  }

  const cartIds = openCarts.map((cart) => cart.id);

  const cartItems = await knex("cart_item as ci")
    .select(
      "ci.*",
      "i.name as item_name",
      "i.price as item_price",
      "i.discounted_price as item_discounted_price",
      "i.metadata as item_metadata",
      "i.unit_size as item_unit_size"
    )
    .leftJoin("item as i", "ci.item_id", "i.id")
    .whereIn("ci.cart_id", cartIds);

  const cartItemsMap = new Map();
  cartItems.forEach((item) => {
    if (!cartItemsMap.has(item.cart_id)) {
      cartItemsMap.set(item.cart_id, []);
    }
    cartItemsMap.get(item.cart_id).push({
      id: item.id,
      cart_id: item.cart_id,
      item_id: item.item_id,
      quantity: item.quantity,
      created_at: item.created_at,
      updated_at: item.updated_at,
      name: item.item_name,
      price: item.item_price,
      discounted_price: item.item_discounted_price,
      metadata: item.item_metadata,
      unit_size: item.item_unit_size,
    });
  });

  const userCartsMap = new Map();
  openCarts.forEach((cart) => {
    const items = cartItemsMap.get(cart.id) || [];

    const total_quantity = items.reduce((sum, item) => sum + item.quantity, 0);

    userCartsMap.set(cart.user_id, {
      ...cart,
      cartItems: items,
      is_open: true,
      total_quantity: total_quantity,
      subtotal: cart.subtotal,
    });
  });

  users.forEach((user) => {
    user.open_cart = userCartsMap.get(user.id) || null;
  });
};

// Function to calculate last order date for users
const calculateLastOrderDates = async (users: User[]) => {
  const userIds = users.map((user) => user.id);

  const lastOrders = await knex("order_detail")
    .select("user_id")
    .select(knex.raw("MAX(date_submitted) as last_order_date"))
    .whereIn("user_id", userIds)
    .groupBy("user_id");

  const lastOrderMap = new Map();
  lastOrders.forEach((row) => {
    lastOrderMap.set(row.user_id, row.last_order_date);
  });

  // Add last_order_date to each user
  users.forEach((user) => {
    user.last_order_date = lastOrderMap.get(user.id) || null;
  });
};

const UsersByFilter = async (_, args: QueryUsersByFilterArgs) => {
  const {
    supplierId,
    filters,
    sortBy: { field: sortField, ordering: sortOrdering } = {
      field: "name",
      ordering: Ordering.Asc,
    },
    pagination: { offset, limit } = { offset: 0, limit: 100 },
    includeCustomPrices,
  } = args.getUsersByFilterInput;

  // Check if we need to sort by unpaid balance or filter by hasUnpaidBalance
  const needsUnpaidBalanceCalculation =
    sortField === "unpaid_balance" || filters?.hasUnpaidBalance !== undefined;

  // Check if we need to sort by last order date
  const needsLastOrderDateCalculation = sortField === "last_order_date";

  let query = knex({ u: "attain_user" })
    .select("u.*")
    .innerJoin({ st: "supplier_times" }, "u.id", "st.business_id")
    .where("st.supplier_id", supplierId)
    .where("u.supplier_beta", true)
    .whereNot("archived", true);

  // Add unpaid balance calculation to query if needed for sorting or filtering
  if (needsUnpaidBalanceCalculation) {
    query = query
      .leftJoin(
        knex("invoice")
          .select("user_id")
          .select(
            knex.raw(
              "sum(COALESCE(subtotal, 0) - COALESCE(paid, 0) - COALESCE(credit, 0)) as unpaid_balance"
            )
          )
          .whereNot("archived", true)
          .whereRaw(
            "ABS(COALESCE(subtotal, 0) - COALESCE(paid, 0) - COALESCE(credit, 0)) > 1e-10"
          )
          .groupBy("user_id")
          .as("ub"),
        "u.id",
        "ub.user_id"
      )
      .select(knex.raw("COALESCE(ub.unpaid_balance, 0) as unpaid_balance"));
  }

  // Handle hasOpenCart filter
  if (filters?.hasOpenCart !== undefined) {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

    if (filters.hasOpenCart) {
      query = query.whereExists(function () {
        this.select("*")
          .from("cart as c")
          .whereRaw("c.user_id = u.id")
          .where("c.updated_at", "<", oneHourAgo)
          .where(function () {
            this.where("c.subtotal", ">", 0).orWhereExists(function () {
              this.select("*")
                .from("cart_item")
                .whereRaw("cart_item.cart_id = c.id");
            });
          });
      });
    } else {
      query = query.whereNotExists(function () {
        this.select("*")
          .from("cart as c")
          .whereRaw("c.user_id = u.id")
          .where("c.updated_at", "<", oneHourAgo)
          .where(function () {
            this.where("c.subtotal", ">", 0).orWhereExists(function () {
              this.select("*")
                .from("cart_item")
                .whereRaw("cart_item.cart_id = c.id");
            });
          });
      });
    }
  }

  // Add last order date calculation to query if needed for sorting
  if (needsLastOrderDateCalculation) {
    query = query
      .leftJoin(
        knex("order_detail")
          .select("user_id")
          .select(knex.raw("MAX(date_submitted) as last_order_date"))
          .groupBy("user_id")
          .as("lod"),
        "u.id",
        "lod.user_id"
      )
      .select(knex.raw("lod.last_order_date"));
  }

  if (filters?.searchTerm && filters.searchTerm.length > 0) {
    query = query.where((builder) => {
      builder
        .where("u.name", "ilike", `%${filters.searchTerm}%`)
        .orWhere("u.address", "ilike", `%${filters.searchTerm}%`)
        .orWhere("u.store_group", "ilike", `%${filters.searchTerm}%`)
        .orWhere("u.owner_name", "ilike", `%${filters.searchTerm}%`);
    });
  }

  if (filters?.route && filters.route !== "all") {
    if (filters.route === "unassigned") {
      query = query.whereRaw("COALESCE(u.route_id, '') = ''");
    } else {
      query = query.where("u.route_id", filters.route);
    }
  }

  if (filters?.active && filters.active !== "all") {
    const isFilteringForActive = filters.active === "Yes";
    query = query.whereRaw(
      `
      CASE 
        WHEN config IS NULL THEN ?
        WHEN config->>'active' IS NULL THEN ?
        ELSE (config->>'active')::boolean = ?
      END
    `,
      [isFilteringForActive, isFilteringForActive, isFilteringForActive]
    );
  }

  if (filters?.driver && filters.driver !== "all_drivers_default") {
    const routeIds = await knex("route")
      .select("id")
      .where("supplier_id", supplierId)
      .where("driver", filters.driver);

    query = query.where((builder) => {
      builder.whereIn(
        "u.route_id",
        routeIds.map((route) => route.id)
      );
    });
  }

  // Filter by hasUnpaidBalance if specified
  if (filters?.hasUnpaidBalance !== undefined) {
    if (filters.hasUnpaidBalance) {
      // Only users with unpaid balance > 0
      query = query.whereRaw("COALESCE(ub.unpaid_balance, 0) > 1e-10");
    } else {
      // Only users with no unpaid balance (= 0)
      query = query.whereRaw("COALESCE(ub.unpaid_balance, 0) = 0");
    }
  }

  // Sort fields - handle unpaid_balance, last_order_date, and case-insensitive name sorting
  if (sortField === "unpaid_balance") {
    query = query.orderBy("unpaid_balance", sortOrdering);
  } else if (sortField === "last_order_date") {
    if (sortOrdering === Ordering.Asc) {
      query = query.orderByRaw("last_order_date ASC NULLS LAST");
    } else {
      query = query.orderByRaw("last_order_date DESC NULLS LAST");
    }
  } else if (sortField === "name") {
    query = query.orderByRaw(`LOWER(u.name) ${sortOrdering}`);
  } else {
    query = query.orderBy(`u.${sortField}`, sortOrdering);
  }

  const total = await query;
  if (offset || limit) {
    query = query.offset(offset).limit(limit);
  }

  const users = await query;
  await getUsersSuppliers(users);
  if (includeCustomPrices) {
    await populateUsersCustomPrices(users);
    await populateUsersCustomUOMPrices(users, supplierId);
  }

  // Populate delivery windows for all users
  await populateDeliveryWindows(users);

  // Calculate unpaid balances for all users if not already done in query
  if (!needsUnpaidBalanceCalculation) {
    await calculateUnpaidBalances(users);
  } else {
    // If we already calculated in query, just ensure the field is properly set
    users.forEach((user) => {
      if (!user.unpaid_balance) {
        user.unpaid_balance = 0;
      }
    });
  }

  await populateOpenCarts(users);

  // Calculate last order dates for all users if not already done in query
  if (!needsLastOrderDateCalculation) {
    await calculateLastOrderDates(users);
  }

  return { users, totalCount: total.length };
};

export default UsersByFilter;
