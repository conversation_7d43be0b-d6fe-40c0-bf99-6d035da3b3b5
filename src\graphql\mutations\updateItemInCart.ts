import knex from "../../../knex/knex";
import { Cart, MutationUpdateItemInCartArgs } from "../../generated/graphql";
import getCart from "../../services/cartService/getCart";

const updateItemInCart = async (_, args: MutationUpdateItemInCartArgs) => {
  const {
    cartId,
    itemId,
    quantity,
    itemUomId,
    notes,
    customPrice,
    minSalePrice,
  } = args.updateItemInCartInput;

  try {
    await knex.transaction(async (trx) => {
      // Check for existing cart item with same item_id, cart_id, and item_uom_id
      let query = trx("cart_item")
        .select("*")
        .where("item_id", itemId)
        .where("cart_id", cartId);

      // Handle item_uom_id - need to check for both null and specific values
      if (itemUomId === null || itemUomId === undefined) {
        query = query.whereNull("item_uom_id");
      } else {
        query = query.where("item_uom_id", itemUomId);
      }

      const items = await query;

      const now = new Date();

      if (items.length === 0) {
        await trx("cart_item").insert({
          cart_id: cartId,
          item_id: itemId,
          quantity: quantity,
          item_uom_id: itemUomId,
          created_at: now,
          updated_at: now,
          notes: notes,
          custom_price: customPrice,
          min_sale_price: minSalePrice,
        });
      } else {
        if (quantity === 0) {
          // Delete with same uniqueness constraints
          let deleteQuery = trx("cart_item")
            .where("item_id", itemId)
            .where("cart_id", cartId);

          if (itemUomId === null || itemUomId === undefined) {
            deleteQuery = deleteQuery.whereNull("item_uom_id");
          } else {
            deleteQuery = deleteQuery.where("item_uom_id", itemUomId);
          }

          await deleteQuery.del();
        } else {
          // Updating existing item quantity
          const updateData: any = { quantity, updated_at: now };
          if (itemUomId !== undefined) {
            updateData.item_uom_id = itemUomId;
          }
          if (notes !== undefined) {
            updateData.notes = notes;
          }
          if (customPrice !== undefined) {
            updateData.custom_price = customPrice;
          }
          if (minSalePrice !== undefined) {
            updateData.min_sale_price = minSalePrice;
          }

          // Update with same uniqueness constraints
          let updateQuery = trx("cart_item")
            .update(updateData)
            .where("item_id", itemId)
            .where("cart_id", cartId);

          if (itemUomId === null || itemUomId === undefined) {
            updateQuery = updateQuery.whereNull("item_uom_id");
          } else {
            updateQuery = updateQuery.where("item_uom_id", itemUomId);
          }

          await updateQuery;
        }
      }

      const cartItemsWithPrices = await trx("cart_item as ci")
        .join("item as i", "ci.item_id", "i.id")
        .where("ci.cart_id", cartId)
        .select(
          "ci.quantity",
          knex.raw("COALESCE(i.discounted_price, i.price) as price")
        );

      const subtotal = cartItemsWithPrices.reduce((sum, item) => {
        return sum + item.price * item.quantity;
      }, 0);

      await trx("cart").where("id", cartId).update({
        subtotal: subtotal,
        updated_at: now,
      });
    });

    const cartDetails: Cart = await getCart(cartId);
    return cartDetails;
  } catch (error) {
    console.error("Error updating cart item:", error);
    throw error;
  }
};

export default updateItemInCart;
