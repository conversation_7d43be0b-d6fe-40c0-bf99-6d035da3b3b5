// src/graphql/mutations/removeStoreAssignments.ts

import { removeStoreAssignments } from "../../services/storeAssignmentService";

const removeStoreAssignmentsResolver = async (_, { input }) => {
  const { employeeId, storeIds } = input;

  try {
    await removeStoreAssignments(employeeId, storeIds);
    return true;
  } catch (error) {
    console.error("Error in removeStoreAssignments mutation:", error);
    throw new Error("Failed to remove store assignments");
  }
};

export default removeStoreAssignmentsResolver;
