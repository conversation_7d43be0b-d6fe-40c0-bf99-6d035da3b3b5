"use strict";

var dbm;
var type;
var seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

const tableName = "route";

exports.up = function (db) {
  return db.addColumn(tableName, "order_day", {
    type: "string",
    length: 50,
  });
};

exports.down = function (db) {
  return db.removeColumn(tableName, "order_day");
};

exports._meta = {
  version: 1,
};
