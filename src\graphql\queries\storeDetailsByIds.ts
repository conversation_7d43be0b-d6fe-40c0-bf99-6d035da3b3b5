// src/graphql/queries/getStoreDetailsByIds.ts
import { getStoreDetailsByIds } from "../../services/storeAssignmentService";

const storeDetailsByIds = async (_, { storeIds, limit = 50 }) => {
  try {
    return await getStoreDetailsByIds(storeIds, limit);
  } catch (error) {
    console.error("Error in getStoreDetailsByIds query:", error);
    throw new Error("Failed to fetch store details");
  }
};

export default storeDetailsByIds;
