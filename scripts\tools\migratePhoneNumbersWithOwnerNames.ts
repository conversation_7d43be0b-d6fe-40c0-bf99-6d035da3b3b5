import knex from "../../knex/knex";

/*
 * Migration script to extract owner names from phone_number field and move them to owner_name field
 *
 * This script handles various formats:
 * - "************ Yogi"
 * - "Yogi ************"
 * - "Yogi-************"
 * - "************-Yogi"
 * - And other variations
 *
 * Only processes records where a valid phone number exists in the phone_number field.
 *
 * Usage: npx ts-node scripts/tools/migratePhoneNumbersWithOwnerNames.ts
 */

interface UserPhoneRecord {
  id: number;
  name: string;
  phone_number: string;
  owner_name: string | null;
}

interface ParsedPhoneData {
  phoneNumber: string;
  ownerName: string;
}

interface MigrationResult {
  id: number;
  name: string;
  originalPhoneNumber: string;
  extractedPhoneNumber: string;
  extractedOwnerName: string;
  success: boolean;
  error?: string;
}

// Configuration
const BATCH_SIZE = 500; // Can be modified
const RUN_ALL_BATCHES = true; // Set to false to run only one batch
const DRY_RUN = true; // Set to true to see what would be changed without making actual changes

// Phone number regex patterns - ordered by specificity (most specific first)
const PHONE_PATTERNS = [
  // International with parentheses: +****************, +****************
  /(\+1[-.\s]?\(\d{3}\)[-.\s]?\d{3}[-.\s]?\d{4})/,
  // International without parentheses: ******-456-7890, +1 ************, +1234567890
  /(\+1[-.\s]?\d{3}[-.\s]?\d{3}[-.\s]?\d{4})/,
  // International 11-digit format: +11234567890, ******4567890
  /(\+1\d{10})/,
  // 11-digit with parentheses: ****************, 1(123)456-7890
  /(1\s*\(\d{3}\)\s*\d{3}[-.\s]?\d{4})/,
  // Format with parentheses: (*************, (123)456-7890, (*************
  /(\(\d{3}\)\s*\d{3}[-.\s]?\d{4})/,
  // 11-digit number starting with 1: **************, 1 ************, 11234567890
  /(1[-.\s]?\d{3}[-.\s]?\d{3}[-.\s]?\d{4})/,
  // Standard formats without parentheses: ************, ************, ************
  /(\d{3}[-.\s]\d{3}[-.\s]\d{4})/,
  // 10 digit number without separators: 1234567890 (only if not part of longer number)
  /(?<!\d)(\d{10})(?!\d)/,
];

/**
 * Validates if a string contains a valid phone number
 */
function containsValidPhoneNumber(text: string): boolean {
  if (!text || typeof text !== "string") return false;

  return PHONE_PATTERNS.some((pattern) => pattern.test(text));
}

/**
 * Extracts phone number and owner name from a combined string
 */
function parsePhoneNumberWithOwner(
  phoneNumberField: string
): ParsedPhoneData | null {
  if (!phoneNumberField || typeof phoneNumberField !== "string") {
    return null;
  }

  const trimmed = phoneNumberField.trim();

  // Find the best phone number match
  let phoneMatch;

  for (const pattern of PHONE_PATTERNS) {
    phoneMatch = trimmed.match(pattern);
    if (phoneMatch) {
      break;
    }
  }

  if (!phoneMatch) {
    return null; // No valid phone number found
  }

  const phoneNumber = phoneMatch[1];

  // If the entire string is just the phone number, no owner name to extract
  if (
    trimmed === phoneNumber ||
    trimmed.replace(/\s+/g, "") === phoneNumber.replace(/\s+/g, "")
  ) {
    return {
      phoneNumber: phoneNumber,
      ownerName: "",
    };
  }

  // Extract owner name by removing the phone number and cleaning up
  let ownerName = trimmed;

  // Remove the exact phone number match
  ownerName = ownerName.replace(phoneNumber, "");

  // Clean up common separators and extra whitespace
  ownerName = ownerName
    .replace(/[-\s]+/g, " ") // Replace multiple dashes/spaces with single space
    .replace(/^[-\s]+|[-\s]+$/g, "") // Remove leading/trailing separators
    .replace(/\s+/g, " ") // Normalize multiple spaces to single space
    .trim();

  // Handle extension separators and cleanup
  // Check for various extension patterns and remove everything after them
  const extensionPatterns = [
    /[x#:](ext|extension)\.?\d+/i, // x123, #ext.456, :extension123
    /[x#:]\d+/i, // x123, #456, :789
    /(ext|extension)\.?\d+/i, // ext123, extension.456, ext.789
    /[x#:](ext|extension)/i, // x, #ext, :extension
  ];

  for (const pattern of extensionPatterns) {
    const extMatch = ownerName.match(pattern);
    if (extMatch) {
      ownerName = ownerName.substring(0, extMatch.index).trim();
      break; // Stop after first match
    }
  }

  // Remove standalone extension patterns that might remain
  ownerName = ownerName
    .replace(/\b(ext|extension|x)\.?\s*\d+\b/gi, "") // Remove "ext 123", "extension.456", "x789"
    .replace(/\b(ext|extension|x)\.?\b$/gi, "") // Remove trailing "ext", "extension.", "x"
    .replace(/^(at|call|contact)\s+/i, "") // Remove "at", "call", "contact" prefixes
    .replace(/\s+(at|call|contact)$/i, "") // Remove "at", "call", "contact" suffixes
    .replace(/[()]/g, "") // Remove any remaining parentheses
    .replace(/:\s*$/, "") // Remove trailing colon
    .replace(/\s+/g, " ") // Normalize spaces
    .trim();

  // Filter out generic terms, single digits/letters, and extension artifacts
  const genericTerms =
    /^(owner|manager|store|station|office|main|front|desk|reception|admin|ext|extension|x|phone|tel|telephone|fax|cell|mobile|work|home|business)$/i;
  const singleCharOrDigit = /^[a-z0-9]$/i;
  const extensionArtifacts = /^(ext\.?\d*|x\d*|#\d*|:\d*)$/i;

  if (
    genericTerms.test(ownerName) ||
    singleCharOrDigit.test(ownerName) ||
    extensionArtifacts.test(ownerName)
  ) {
    ownerName = "";
  }

  // Filter out if it's just digits (likely extension or phone fragment)
  if (/^\d+$/.test(ownerName)) {
    ownerName = "";
  }

  // Filter out common extension formats that might have been missed
  if (/^(ext|extension|x)\.?\d*$/i.test(ownerName)) {
    ownerName = "";
  }

  return {
    phoneNumber: phoneNumber,
    ownerName: ownerName,
  };
}

/**
 * Fetches users with phone numbers that potentially contain owner names
 */
async function fetchUsersWithPhoneNumbers(
  offset: number,
  limit: number
): Promise<UserPhoneRecord[]> {
  return await knex("attain_user")
    .select("id", "name", "phone_number", "owner_name")
    .whereNotNull("phone_number")
    .where("phone_number", "!=", "")
    .orderBy("id")
    .offset(offset)
    .limit(limit);
}

/**
 * Updates a user's phone_number and owner_name fields
 */
async function updateUserPhoneData(
  userId: number,
  newOwnerName: string
): Promise<void> {
  if (DRY_RUN) {
    console.log(
      `[DRY RUN] Would update user ${userId}, owner_name="${newOwnerName}"`
    );
    return;
  }

  await knex("attain_user").where("id", userId).update({
    owner_name: newOwnerName,
    updated_at: knex.fn.now(),
  });
}

/**
 * Processes a batch of users
 */
async function processBatch(
  users: UserPhoneRecord[]
): Promise<MigrationResult[]> {
  const results: MigrationResult[] = [];

  for (const user of users) {
    try {
      // Skip if phone_number is just a name without any numbers
      if (!containsValidPhoneNumber(user.phone_number)) {
        results.push({
          id: user.id,
          name: user.name,
          originalPhoneNumber: user.phone_number,
          extractedPhoneNumber: user.phone_number, // Keep as is
          extractedOwnerName: user.owner_name || "",
          success: false,
          error: "No valid phone number found",
        });
        continue;
      }

      const parsed = parsePhoneNumberWithOwner(user.phone_number);

      if (!parsed) {
        results.push({
          id: user.id,
          name: user.name,
          originalPhoneNumber: user.phone_number,
          extractedPhoneNumber: user.phone_number,
          extractedOwnerName: user.owner_name || "",
          success: false,
          error: "Failed to parse phone number",
        });
        continue;
      }

      // Determine the final owner name (prefer existing owner_name if it exists and parsed name is empty)
      let finalOwnerName = parsed.ownerName;
      if (!finalOwnerName && user.owner_name) {
        finalOwnerName = user.owner_name;
      }

      // Only update if there's actually a change needed
      const phoneChanged = user.phone_number !== parsed.phoneNumber;
      const ownerChanged = (user.owner_name || "") !== finalOwnerName;

      if (phoneChanged || ownerChanged) {
        await updateUserPhoneData(user.id, finalOwnerName);

        results.push({
          id: user.id,
          name: user.name,
          originalPhoneNumber: user.phone_number,
          extractedPhoneNumber: parsed.phoneNumber,
          extractedOwnerName: finalOwnerName,
          success: true,
        });
      } else {
        results.push({
          id: user.id,
          name: user.name,
          originalPhoneNumber: user.phone_number,
          extractedPhoneNumber: parsed.phoneNumber,
          extractedOwnerName: finalOwnerName,
          success: false,
          error: "No changes needed",
        });
      }
    } catch (error) {
      results.push({
        id: user.id,
        name: user.name,
        originalPhoneNumber: user.phone_number,
        extractedPhoneNumber: "",
        extractedOwnerName: "",
        success: false,
        error: error.message,
      });
    }
  }

  return results;
}

/**
 * Main migration function
 */
async function migratePhoneNumbers() {
  try {
    console.log("🚀 Starting phone number migration...\n");
    console.log(`Configuration:`);
    console.log(`- Batch size: ${BATCH_SIZE}`);
    console.log(`- Run all batches: ${RUN_ALL_BATCHES}`);
    console.log(`- Dry run: ${DRY_RUN}\n`);

    // Get total count of users with phone numbers
    const totalCount = await knex("attain_user")
      .whereNotNull("phone_number")
      .where("phone_number", "!=", "")
      .count("id as count")
      .first();

    const total = parseInt((totalCount?.count as string) || "0");
    const totalBatches = Math.ceil(total / BATCH_SIZE);

    console.log(`📊 Found ${total} users with phone numbers`);
    console.log(
      `📦 Will process in ${totalBatches} batches of ${BATCH_SIZE}\n`
    );

    if (total === 0) {
      console.log("No users found with phone numbers to process.");
      return;
    }

    let offset = 0;
    let batchNumber = 1;
    let totalProcessed = 0;
    let totalUpdated = 0;
    let totalErrors = 0;
    const allResults: MigrationResult[] = [];

    while (offset < total) {
      console.log(
        `🔄 Processing batch ${batchNumber}/${totalBatches} (offset: ${offset})...`
      );

      // Fetch batch
      const users = await fetchUsersWithPhoneNumbers(offset, BATCH_SIZE);

      if (users.length === 0) {
        console.log("No more users to process.");
        break;
      }

      // Process batch
      const batchResults = await processBatch(users);
      allResults.push(...batchResults);

      // Count results
      const batchUpdated = batchResults.filter((r) => r.success).length;
      const batchErrors = batchResults.filter((r) => !r.success).length;

      totalProcessed += users.length;
      totalUpdated += batchUpdated;
      totalErrors += batchErrors;

      console.log(
        `✅ Batch ${batchNumber} completed: ${batchUpdated} updated, ${batchErrors} errors`
      );

      // Show some examples from this batch
      const examples = batchResults.filter((r) => r.success).slice(0, 3);
      if (examples.length > 0) {
        console.log("   Examples:");
        examples.forEach((ex) => {
          console.log(
            `   - ID ${ex.id}: "${ex.originalPhoneNumber}" → phone: "${ex.extractedPhoneNumber}", owner: "${ex.extractedOwnerName}"`
          );
        });
      }

      console.log();

      // Move to next batch
      offset += BATCH_SIZE;
      batchNumber++;

      // If not running all batches, stop after first batch
      if (!RUN_ALL_BATCHES) {
        console.log("🛑 Stopping after one batch as configured.\n");
        break;
      }
    }

    // Final summary
    console.log("📈 MIGRATION SUMMARY");
    console.log("=".repeat(50));
    console.log(`Total users processed: ${totalProcessed}`);
    console.log(`Successfully updated: ${totalUpdated}`);
    console.log(`Errors/No changes: ${totalErrors}`);
    console.log(`Batches processed: ${batchNumber - 1}`);

    // Show error breakdown
    const errorTypes = new Map<string, number>();
    allResults
      .filter((r) => !r.success)
      .forEach((r) => {
        const errorType = r.error || "Unknown error";
        errorTypes.set(errorType, (errorTypes.get(errorType) || 0) + 1);
      });

    if (errorTypes.size > 0) {
      console.log("\n❌ ERROR BREAKDOWN:");
      errorTypes.forEach((count, error) => {
        console.log(`   ${error}: ${count} occurrences`);
      });
    }

    // Export detailed results
    const fs = require("fs");
    const path = require("path");
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const exportPath = path.join(
      __dirname,
      `phone_migration_results_${timestamp}.json`
    );

    const exportData = {
      summary: {
        totalProcessed,
        totalUpdated,
        totalErrors,
        batchesProcessed: batchNumber - 1,
        migrationDate: new Date().toISOString(),
        dryRun: DRY_RUN,
      },
      successfulUpdates: allResults
        .filter((r) => r.success)
        .map((r) => ({
          id: r.id,
          originalPhoneNumber: r.originalPhoneNumber,
          extractedPhoneNumber: r.extractedPhoneNumber,
          extractedOwnerName: r.extractedOwnerName,
        })),
      errorSummary: {
        totalErrors,
        errorBreakdown: Array.from(errorTypes.entries()).map(
          ([error, count]) => ({
            error,
            count,
          })
        ),
      },
    };

    fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2));
    console.log(`\n💾 Detailed results exported to: ${exportPath}`);
  } catch (error) {
    console.error("❌ Migration failed:", error);
    throw error;
  } finally {
    await knex.destroy();
    console.log("\n🔚 Database connection closed");
  }
}

// Run the migration
if (require.main === module) {
  migratePhoneNumbers()
    .then(() => {
      console.log("\n✅ Phone number migration completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Migration failed:", error);
      process.exit(1);
    });
}

export {
  migratePhoneNumbers,
  parsePhoneNumberWithOwner,
  containsValidPhoneNumber,
};
