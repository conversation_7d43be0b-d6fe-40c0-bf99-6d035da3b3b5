"use strict";

var dbm;
var type;
var seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

const tableName = "verification_otps";

exports.up = function (db) {
  return db
    .createTable(tableName, {
      id: { type: "int", primaryKey: true, autoIncrement: true },
      user_id: {
        type: "int",
        notNull: true,
        foreignKey: {
          name: "fk_verification_otps_user",
          table: "attain_user",
          mapping: "id",
          rules: {
            onDelete: "CASCADE",
            onUpdate: "RESTRICT",
          },
        },
      },
      type: { type: "string", length: 50, notNull: true }, // 'password_reset' or 'username_recovery'
      code: { type: "string", length: 6, notNull: true },
      channel: { type: "string", length: 10, notNull: true }, // 'sms' or 'voice'
      expires_at: { type: "timestamp", notNull: true },
      used: { type: "boolean", defaultValue: false },
      created_at: {
        type: "timestamp",
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: "timestamp",
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
    })
    .then(() =>
      db.addIndex(tableName, "idx_verification_otps_user_type", [
        "user_id",
        "type",
      ])
    )
    .then(() =>
      db.addIndex(tableName, "idx_verification_otps_expires_at", ["expires_at"])
    )
    .then(() => db.addIndex(tableName, "idx_verification_otps_code", ["code"]));
};

exports.down = function (db) {
  return db.dropTable(tableName);
};

exports._meta = {
  version: 1,
};
