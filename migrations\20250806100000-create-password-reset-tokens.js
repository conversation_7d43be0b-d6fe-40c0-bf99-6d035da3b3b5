"use strict";

var dbm;
var type;
var seed;

exports.setup = function (options, seedLink) {
  dbm = options.dbmigrate;
  type = dbm.dataType;
  seed = seedLink;
};

const tableName = "password_reset_tokens";

exports.up = function (db) {
  return db
    .createTable(tableName, {
      id: { type: "int", primaryKey: true, autoIncrement: true },
      user_id: {
        type: "int",
        notNull: true,
        foreignKey: {
          name: "fk_password_reset_tokens_user",
          table: "attain_user",
          mapping: "id",
          rules: {
            onDelete: "CASCADE",
            onUpdate: "RESTRICT",
          },
        },
      },
      token: { type: "string", length: 255, notNull: true, unique: true }, // e.g., UUID
      expires_at: { type: "timestamp", notNull: true },
      used: { type: "boolean", defaultValue: false },
      created_at: {
        type: "timestamp",
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: "timestamp",
        defaultValue: new String("CURRENT_TIMESTAMP"),
      },
    })
    .then(() =>
      db.addIndex(tableName, "idx_password_reset_tokens_token", ["token"], true)
    )
    .then(() =>
      db.addIndex(tableName, "idx_password_reset_tokens_user", ["user_id"])
    )
    .then(() =>
      db.addIndex(tableName, "idx_password_reset_tokens_expires_at", [
        "expires_at",
      ])
    );
};

exports.down = function (db) {
  return db.dropTable(tableName);
};

exports._meta = {
  version: 1,
};
