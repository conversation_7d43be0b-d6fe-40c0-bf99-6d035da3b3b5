import knex from "../../../knex/knex";
import { MutationCreateUserArgs } from "../../generated/graphql";
import axios from "axios";
import { initializeApp } from "firebase/app";
import { getAuth, createUserWithEmailAndPassword } from "firebase/auth";
import { getFirestore, doc, setDoc } from "firebase/firestore";
import sendTextMessage, {
  cleanupPhoneNumber,
} from "../../services/notificationService/sendTextMessages";
import {
  dsdSupplierConfigMap,
  SupplierConfig,
} from "../../constants/suppliers";
import {
  getGroupCustomPrices,
  updateUser,
  updateUserCustomPrices,
  upsertDeliveryWindow,
} from "../../services/userService/userService";
import {
  updateUserCustomUOMPrices,
  getGroupCustomUOMPrices,
  updateGroupCustomUOMPrices,
} from "../../services/itemService/uomService";
import {
  updateCustomerHiddenProducts,
  getGroupHiddenProducts,
} from "../../services/itemService/customerHiddenProductsService";

const firebaseConfig = {
  apiKey: "AIzaSyDazkUMxMUnWRpp19dAH31_TXJ3xrQL3RE",
  authDomain: "attain-23279.firebaseapp.com",
  projectId: "attain-23279",
  storageBucket: "attain-23279.appspot.com",
  messagingSenderId: "556882473265",
  appId: "1:556882473265:web:a92cd9a5294dc755f38010",
  measurementId: "G-H6QGK63XJL",
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

const createUser = async (_, args: MutationCreateUserArgs) => {
  const {
    name,
    password,
    user_name,
    suppliers,
    address,
    phone_number,
    created_by,
    ein,
    custom_prices,
    custom_uom_prices,
    store_group,
    delivery_window,
    hidden_products,
    net_terms_days,
    ...otherOptionalFields
  } = args.createUserInput;

  const login =
    created_by === "self-serve"
      ? user_name
      : name
          .toLowerCase()
          .replaceAll(" ", "-")
          .replaceAll("'", "")
          .replaceAll("’", "");
  const email = login + "@joinattain.com";

  let beta = false;
  let pickedSupplierConfig: SupplierConfig = {
    name: "Default",
  };

  Object.keys(dsdSupplierConfigMap).forEach((key) => {
    if (suppliers.includes(key)) {
      beta = true;
      pickedSupplierConfig = dsdSupplierConfigMap[key];
    }
  });

  let userId: number;
  let firebaseUser;

  // Create Firebase user if password is provided
  if (password) {
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      firebaseUser = userCredential.user;
    } catch (error) {
      console.error(
        "User Creation Firebase Error",
        error.code,
        error.message,
        args.createUserInput
      );
      throw error;
    }
  }

  try {
    const result = await knex.transaction(async (txn) => {
      const [newUser] = await txn("attain_user")
        .insert({
          ...otherOptionalFields,
          address,
          email,
          name,
          user_name: user_name || name,
          phone_number: phone_number,
          password: password,
          created_by: created_by,
          supplier_beta: beta ? true : false,
          approved: pickedSupplierConfig.needs_approval ? false : true,
          ein: ein,
          archived: false,
          store_group,
          net_terms_days,
        })
        .returning("id");

      userId = newUser.id;

      await txn("cart").insert({
        id: userId,
        subtotal: 0,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date(),
      });

      await txn("supplier_times").insert(
        suppliers.map((supplier_id) => ({
          business_id: userId,
          supplier_id,
        }))
      );

      if (store_group) {
        // Apply group prices (both regular and UOM)
        const groupPrices = await getGroupCustomPrices(store_group);
        await updateUserCustomPrices(txn, userId.toString(), groupPrices);

        const firstSupplierId = suppliers[0]; // Get first supplier ID for group UOM prices
        const groupUOMPrices = await getGroupCustomUOMPrices(
          store_group,
          firstSupplierId
        );
        if (groupUOMPrices && groupUOMPrices.length > 0) {
          await updateUserCustomUOMPrices(
            txn,
            userId.toString(),
            groupUOMPrices
          );
        }
      } else if (
        (custom_prices && custom_prices.length > 0) ||
        (custom_uom_prices && custom_uom_prices.length > 0)
      ) {
        // Apply individual prices (both regular and UOM)
        if (custom_prices && custom_prices.length > 0) {
          await updateUserCustomPrices(txn, userId.toString(), custom_prices);
        }

        if (custom_uom_prices && custom_uom_prices.length > 0) {
          await updateUserCustomUOMPrices(
            txn,
            userId.toString(),
            custom_uom_prices
          );
        }
      }

      // Handle hidden products
      if (store_group) {
        const groupHiddenProducts = await getGroupHiddenProducts(
          suppliers[0],
          store_group
        );
        await updateCustomerHiddenProducts(
          txn,
          suppliers[0],
          userId.toString(),
          groupHiddenProducts
        );
      } else if (hidden_products && hidden_products.length > 0) {
        await updateCustomerHiddenProducts(
          txn,
          suppliers[0],
          userId.toString(),
          hidden_products
        );
      }

      // Handle hidden products
      if (store_group) {
        const groupHiddenProducts = await getGroupHiddenProducts(
          suppliers[0],
          store_group
        );
        await updateCustomerHiddenProducts(
          txn,
          suppliers[0],
          userId.toString(),
          groupHiddenProducts
        );
      } else if (hidden_products && hidden_products.length > 0) {
        await updateCustomerHiddenProducts(
          txn,
          suppliers[0],
          userId.toString(),
          hidden_products
        );
      }

      // Handle delivery window if provided
      if (delivery_window) {
        await upsertDeliveryWindow(txn, userId.toString(), delivery_window);
      }

      // Handle hidden products
      if (store_group) {
        const groupHiddenProducts = await getGroupHiddenProducts(
          suppliers[0],
          store_group
        );
        await updateCustomerHiddenProducts(
          txn,
          suppliers[0],
          userId.toString(),
          groupHiddenProducts
        );
      } else if (hidden_products && hidden_products.length > 0) {
        await updateCustomerHiddenProducts(
          txn,
          suppliers[0],
          userId.toString(),
          hidden_products
        );
      }

      // Handle delivery window if provided
      if (delivery_window) {
        await upsertDeliveryWindow(txn, userId.toString(), delivery_window);
      }

      const supplierNames = await txn("supplier")
        .select("name")
        .whereIn("id", suppliers);

      return supplierNames;
    });

    // If we have a Firebase user, update their user ID mapping
    if (firebaseUser) {
      try {
        await setDoc(doc(db, "users", firebaseUser.uid), {
          userId: userId.toString(),
        });
      } catch (error) {
        await firebaseUser.delete();
        throw error;
      }
    }

    try {
      await axios.post(
        "*********************************************************************************",
        {
          text: `Created new account for ${name} with ID ${userId}, with Supplier(s): ${result
            .map((s) => s.name)
            .join(", ")} (created by ${created_by})`,
        },
        {
          headers: {
            accept: "application/json",
            "content-type": "application/json",
          },
        }
      );
    } catch (error) {
      console.log("Error sending Slack message", error);
    }

    // Send text message with login credentials for self-serve accounts.
    if (created_by === "self-serve") {
      const signupTextMessage = `Welcome to Attain Wholesale - the official ordering app for ${
        (pickedSupplierConfig as SupplierConfig).name
      }! 🎉\n\nHere are your login details:\nUsername: ${user_name}\nPassword: ${password}\n\nFor support or any questions, contact us at ************. We can't wait for you to start ordering through Attain!`;
      await sendTextMessage(signupTextMessage, cleanupPhoneNumber(phone_number))
        .then((message) => {
          console.log(`Sign-Up Message sent with SID: ${message.sid}`);
        })
        .catch((error) => {
          console.error(`Error sending signup message: ${error}`);
        });

      if ((pickedSupplierConfig as SupplierConfig).needs_approval) {
        const approvalRequestTextMessage = `New signup on Attain!\n\nCustomer: ${name}\n\nAddress: ${address}\n\nPlease approve their account so they can start ordering!`;
        await sendTextMessage(
          approvalRequestTextMessage,
          (pickedSupplierConfig as SupplierConfig).phone_number
        )
          .then((message) => {
            console.log(
              `Approval Request Message sent with SID: ${message.sid}`
            );
          })
          .catch((error) => {
            console.error(`Error sending approval request message: ${error}`);
          });
      }
    }

    return ["Login: " + login, "Password: " + password, userId.toString()];
  } catch (error) {
    if (firebaseUser) {
      await firebaseUser.delete();
    }
    throw error;
  }
};

export default createUser;
