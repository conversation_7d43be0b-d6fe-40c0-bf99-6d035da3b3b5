import knex from "../../../knex/knex";
import axios from "axios";
import getCart from "../../services/cartService/getCart";
import modifyToCustomPricing from "../../services/itemService/modifyToCustomPricing";
import {
  ActivityLogType,
  MutationSubmitOrderArgs,
  Order,
} from "../../generated/graphql";
import { sendOrderDetailsMessage } from "../../services/notificationService/sendTextMessages";
import { USDollarFromNumber } from "../../util/formats";
import { sendNotification } from "../../services/notificationService/sendNotification";
import { sendEmail } from "../../services/notificationService/sendEmail";
import addInvoice from "../../services/invoiceService/addInvoice";
import { dsdSupplierConfigMap } from "../../constants/suppliers";
import dayjs from "dayjs";
import { createInvoiceFromOrder } from "../../services/invoiceService";
import { getSupplierConfig } from "../../services/supplierService";
import { getNextOrderNumber } from "../../services/orderService/createOrder";
import { createActivityLog } from "../../services/accessService/activityLog";

const submitOrder = async (
  _,
  args: MutationSubmitOrderArgs
): Promise<Order | undefined> => {
  const {
    userId,
    cartId,
    supplier,
    notes,
    discount,
    isCredit,
    deliveryDate,
    config,
  } = args.submitOrderInput;

  const cartDetails = await getCart(cartId);

  let cartItems = cartDetails.cartItems;
  if (supplier) {
    const subCart = cartDetails.subCarts.find(
      (sub) => sub.supplier === supplier
    );
    cartItems = subCart.cartItems;
  }
  await modifyToCustomPricing(cartItems, userId);
  if (isCredit) {
    cartItems.forEach((item) => {
      item.quantity = -item.quantity;
    });
  }

  // Get user's delivery fee from config
  const user = await knex("attain_user")
    .select("config")
    .where("id", userId)
    .first();

  const deliveryFee = user?.config?.delivery_fee || 0;

  let subtotal = cartItems.reduce(
    (partialSum, a) =>
      partialSum +
      (parseFloat(
        (a.custom_price as never) ??
          (a.discounted_price as never) ??
          (a.price as never)
      ) || 0.0) *
        a.quantity,
    0.0
  );

  // Add delivery fee to subtotal (but not for credit orders)
  if (deliveryFee > 0 && !isCredit) {
    subtotal += deliveryFee;
  }

  let orderDetails: Order;
  let csvExists, cartCSV;

  const dateNow = new Date();
  const supplierId = (
    await knex.select("id").from("supplier").where("name", supplier).first()
  ).id;

  const supplierConfig = await getSupplierConfig(supplierId);

  if (supplierConfig.requires_delivery_date && !deliveryDate) {
    throw new Error("Delivery date is required");
  }

  try {
    const orderNumber = await getNextOrderNumber(supplier);

    await knex.transaction(async (trx) => {
      const [orderResult] = await trx("order_detail")
        .insert({
          user_id: userId,
          status:
            config?.originalStatus === "submitted"
              ? "Submitted"
              : config?.submitted_by_driver
              ? "In Transit"
              : "submitted",
          subtotal: subtotal,
          date_submitted: dateNow.toUTCString(),
          single_supplier: supplier,
          discount: discount ?? 0,
          notes,
          config,
          order_number: orderNumber,
        })
        .returning("*");

      orderDetails = orderResult;

      // Prepare bulk insert for order items using the database ID
      const orderItems = cartItems.map((item) => ({
        order_id: orderDetails.id,
        item_id: item.item_id,
        quantity: item.quantity,
        price_purchased_at:
          item.custom_price ?? item.discounted_price ?? item.price,
        notes: item.notes || null,
        item_uom_id: item.item_uom_id,
      }));

      // Add delivery fee as line item if > 0 and not credit
      if (deliveryFee > 0 && !isCredit) {
        orderItems.push({
          order_id: orderDetails.id,
          item_id: null, // null item_id identifies delivery fee
          quantity: 1,
          price_purchased_at: deliveryFee,
          notes: null,
          item_uom_id: null,
        });
      }

      // Bulk insert order items
      await trx("order_item").insert(orderItems);

      // Delete cart items and reset cart
      const cartItemIds = cartItems.map((item) => item.id);
      await Promise.all([
        trx("cart_item").whereIn("id", cartItemIds).del(),
        trx("cart")
          .where("id", cartId)
          .update({ csv: "", subtotal: 0.0, updated_at: new Date() }),
      ]);

      // Check for cart CSV and insert into duffl_order if exists
      cartCSV = await trx
        .select()
        .table("cart_csv")
        .where("cart_id", cartId)
        .where("supplier", supplier);

      csvExists =
        cartCSV.length > 0 &&
        typeof cartCSV[0].csv === "string" &&
        cartCSV[0].csv.length > 0;

      if (csvExists) {
        await trx("duffl_order").insert({
          csv: cartCSV[0].csv,
          order_id: orderDetails.id,
          status: "open",
          user_id: userId,
        });

        // Delete from cart_csv
        await trx("cart_csv")
          .where("cart_id", cartId)
          .where("supplier", supplier)
          .del();
      }
      const formattedDeliveryDate = deliveryDate
        ? dayjs(deliveryDate).utc().format("YYYY-MM-DD")
        : null;
      // Create Order Status
      await trx("order_status").insert({
        order_id: orderDetails.id,
        supplier_id: supplierId,
        delivery_date:
          formattedDeliveryDate ??
          (supplierConfig.auto_set_delivery_date
            ? dayjs().utc().format("YYYY-MM-DD")
            : null),
      });
    });

    const business = await knex("attain_user").where("id", userId).first();

    let slackUrl = process.env.SUBMITTED_ORDERS_SLACK_CHANNEL;
    if (dsdSupplierConfigMap[supplierId]?.orderNotificationSlackURL) {
      slackUrl = dsdSupplierConfigMap[supplierId].orderNotificationSlackURL;
    }
    if (userId === "1") {
      slackUrl =
        "*********************************************************************************";
    }

    try {
      await axios.post(
        slackUrl,
        {
          text:
            `${subtotal > 0 ? "Order" : "Credit Memo"} #${
              orderDetails.order_number
            } for ${supplier} submitted by ${
              business.name
            } for ${USDollarFromNumber(subtotal)}` +
            (csvExists ? `: \n${cartCSV[0].csv}` : ""),
        },
        {
          headers: {
            accept: "application/json",
            "content-type": "application/json",
          },
        }
      );
    } catch (error) {
      console.log("Error sending Slack message", error);
    }

    if (
      supplier === "A&I Beverage" ||
      supplier === "A&G Wholesale" ||
      supplier === "Empire Snacks"
    ) {
      try {
        await sendOrderDetailsMessage(orderDetails.id);
      } catch (error) {
        console.log("Error sending order details message", error);
      }
    }

    if (supplier === "Beverage Express") {
      const notification = {
        title: `Order Confirmed`,
        subtitle: ``,
        body: `Your order #${orderDetails.order_number} has been confirmed by ${supplier}! Track your order status in the orders tab.`,
        data: {},
      };
      try {
        await sendNotification([userId], notification);
      } catch (error) {
        console.log("Error sending notification", error);
      }
    }

    await createInvoiceFromOrder(supplierId, orderDetails.id);

    await knex.transaction(async (trx) => {
      await createActivityLog(trx, {
        supplierId: parseInt(supplierId),
        userId: parseInt(userId),
        type: ActivityLogType.OrderSubmitted,
        metadata: {
          order_id: orderDetails.id,
          order_number: orderDetails.order_number,
        },
      });
    });

    return orderDetails;
  } catch (error) {
    console.log(error);
    // Handle error, transaction rolled back
  }
};
export default submitOrder;
