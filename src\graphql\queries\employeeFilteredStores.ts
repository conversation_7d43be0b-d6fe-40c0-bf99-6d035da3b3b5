// src/graphql/queries/employeeFilteredStores.ts - Fixed with proper error handling

import { QueryResolvers } from "../../generated/graphql";
import { getEmployeeFilteredStores } from "../../services/accessService/employeeFilteringService";

const employeeFilteredStores: QueryResolvers["employeeFilteredStores"] = async (
  _,
  { employeeId, supplierId, selectedRouteIds }
) => {
  try {
    // Validate required parameters
    if (!employeeId || !supplierId) {
      console.error("Missing required parameters:", { employeeId, supplierId });
      return {
        stores: [],
        hasAllAccess: false,
      };
    }

    const result = await getEmployeeFilteredStores(
      employeeId,
      supplierId,
      selectedRouteIds || []
    );

    // Ensure we always return a valid structure that matches the schema
    return {
      stores: result.stores || [],
      hasAllAccess: result.hasAllAccess || false,
    };
  } catch (error) {
    console.error("Error in employeeFilteredStores resolver:", error);

    // NEVER throw an error - always return a valid response for non-nullable fields
    return {
      stores: [],
      hasAllAccess: false,
    };
  }
};

export default employeeFilteredStores;
