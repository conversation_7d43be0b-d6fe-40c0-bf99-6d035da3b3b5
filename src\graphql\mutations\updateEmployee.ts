// src/graphql/mutations/updateEmployee.ts

import { MutationResolvers } from "../../generated/graphql";
import updateEmployee from "../../services/accessService/updateEmployee";

const updateEmployeeResolver: MutationResolvers["updateEmployee"] = async (
  _,
  { input }
) => {
  const {
    supplierId,
    id,
    name,
    phone,
    email,
    password,
    appAccess,
    dashboardAccess,
    roleIds,
    routeIds,
    storeIds,
    lastLogin,
    archived,
  } = input;

  try {
    const employee = await updateEmployee({
      supplierId,
      id,
      name,
      phone,
      email,
      password,
      appAccess,
      dashboardAccess,
      roleIds,
      routeIds,
      storeIds,
      lastLogin,
      archived,
    });

    return employee;
  } catch (error) {
    console.error("Error in updateEmployee resolver:", error);
    throw error;
  }
};

export default updateEmployeeResolver;
