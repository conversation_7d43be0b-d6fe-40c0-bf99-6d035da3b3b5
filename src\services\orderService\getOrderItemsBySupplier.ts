import knex from "../../../knex/knex";
import { populateCartItemsUOMs } from "../itemService/uomService";

const getOrderItemsBySupplier = async (orderId, supplier) => {
  const order = await knex
    .select("*")
    .from("order_detail")
    .where("id", orderId);
  let result = [];

  if (order[0].single_supplier && order[0].single_supplier === supplier) {
    // For single supplier orders, get all items including delivery fees
    result = await knex
      .select([
        "order_item.id as id",
        "order_item.order_id",
        "order_item.item_id",
        "order_item.quantity",
        "order_item.price_purchased_at",
        "order_item.notes",
        "order_item.item_uom_id",
        "order_item.new_supplier",
        "item.name",
        "item.unit_size",
        "item.size",
        "item.upc1",
        "item.upc2",
        "item.price",
        "item.cog_price",
        "item.image",
        "item.supplier_code",
        "item.supplier",
        "item.metadata",
        "item.qb_id",
        "item.nacs_category",
        "item.nacs_subcategory",
        "item.crv",
        "item.oos",
        "item.qoh",
        "item.discounted_price",
        "item.archived",
      ])
      .from("order_item")
      .leftJoin("item", "item.id", "order_item.item_id")
      .where("order_item.order_id", orderId)
      .orderBy("order_item.id", "desc")
      .then((items) =>
        items.map((item) => ({
          ...item,
          name: item.item_id === null ? "Delivery Fee" : item.name,
          // Ensure other fields have defaults for delivery fees
          unit_size: item.unit_size || "",
          size: item.size || "",
          upc1: item.upc1 || "",
          upc2: item.upc2 || "",
          price: item.price || item.price_purchased_at,
          cog_price: item.cog_price || 0,
          image: item.image || "",
          supplier_code: item.supplier_code || "",
          supplier: item.supplier || "",
          metadata: item.metadata || "",
          qb_id: item.qb_id || "",
        }))
      );
  } else {
    const orderItems = await knex
      .select([
        "order_item.id as id",
        "order_item.order_id",
        "order_item.item_id",
        "order_item.quantity",
        "order_item.price_purchased_at",
        "order_item.notes",
        "order_item.item_uom_id",
        "order_item.new_supplier",
        "item.name",
        "item.unit_size",
        "item.size",
        "item.upc1",
        "item.upc2",
        "item.price",
        "item.cog_price",
        "item.image",
        "item.supplier_code",
        "item.supplier",
        "item.metadata",
        "item.qb_id",
        "item.nacs_category",
        "item.nacs_subcategory",
        "item.crv",
        "item.oos",
        "item.qoh",
        "item.discounted_price",
        "item.archived",
      ])
      .from("order_item")
      .leftJoin("item", "item.id", "order_item.item_id")
      .where("order_item.order_id", orderId)
      .where(
        (whereBuilder) =>
          whereBuilder
            .where("item.supplier", supplier)
            .orWhere("order_item.new_supplier", supplier)
            .orWhereNull("order_item.item_id") // Include delivery fees
      )
      .then((items) =>
        items.map((item) => ({
          ...item,
          // The id field is now explicitly order_item.id (no override from item.id)
          // Override name for delivery fees
          name: item.item_id === null ? "Delivery Fee" : item.name,
          // Ensure other fields have defaults for delivery fees
          unit_size: item.unit_size || "",
          size: item.size || "",
          upc1: item.upc1 || "",
          upc2: item.upc2 || "",
          price: item.price || item.price_purchased_at,
          cog_price: item.cog_price || 0,
          image: item.image || "",
          supplier_code: item.supplier_code || "",
          supplier: item.supplier || "",
          metadata: item.metadata || "",
          qb_id: item.qb_id || "",
        }))
      );

    for (let i = 0; i < orderItems.length; i++) {
      const cur = orderItems[i];
      if (cur.new_supplier && cur.new_supplier !== supplier) {
        continue;
      } else {
        result.push(cur);
      }
    }
  }

  // Populate UOM data for order items
  await populateCartItemsUOMs(result);

  return {
    orderItems: result,
  };
};

export default getOrderItemsBySupplier;
