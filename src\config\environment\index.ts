import dotenv from "dotenv";
dotenv.config();

export const port = process.env.PORT;

// You may use this as a boolean value for different situations
export const env = {
  development: process.env.NODE_ENV === "development",
  test: process.env.NODE_ENV === "test",
  staging: process.env.NODE_ENV === "staging",
  production: process.env.NODE_ENV === "production",
};

export const postgres = {
  url: process.env.DATABASE_URL,
};

export const scraper = {
  url: process.env.SCRAPER_URL,
  project: process.env.SCRAPER_PROJECT,
};

export const awsS3Config = {
  credentials: {
    accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
  },
  region: process.env.AWS_S3_RESOURCES_REGION,
  bucket: process.env.AWS_S3_RESOURCES_BUCKET,
};

export const algoliaConfig = {
  appId: process.env.ALGOLIA_APP_ID,
  apiKey: process.env.ALGOLIA_API_KEY,
};

export const quickBooksDesktopConfig = {
  userId: process.env.QUICKBOOKS_DESKTOP_USER_ID,
  clientSecret: process.env.QUICKBOOKS_DESKTOP_CLIENT_SECRET,
};

export const sentryConfig = {
  dsn: process.env.SENTRY_DSN,
};
