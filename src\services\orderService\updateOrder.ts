import dayjs from "dayjs";
import knex from "../../../knex/knex";
import { Order, UpdateOrderItemInput, Invoice } from "../../generated/graphql";
import updateInvoiceFromOrder from "../../services/invoiceService/updateInvoiceFromOrder";
import createInvoiceFromOrder from "../invoiceService/createInvoiceFromOrder";
import { getInvoiceByOrderId } from "../invoiceService/getInvoice";

export const updateOrder = async ({
  supplierId,
  userId,
  orderId,
  order,
  orderItems,
  invoice,
}: {
  supplierId: string;
  userId: string;
  orderId: string;
  order: Partial<
    Omit<Order, "id" | "created_at" | "updated_at" | "orderItems">
  > & { netTermsDays?: number };
  orderItems: UpdateOrderItemInput[];
  invoice: Partial<Omit<Invoice, "id" | "created_at" | "updated_at">>;
}) => {
  const mappedOrderFields = {
    order_name: order.orderName,
    notes: order.notes,
    config: order.config,
    subtotal: order.subtotal,
    net_terms_days: order.netTermsDays,
  } as any;

  if (order.status != "Overdue") {
    mappedOrderFields.status = order.status;
  }

  Object.keys(mappedOrderFields).forEach((key) => {
    if (mappedOrderFields[key] === undefined) {
      delete mappedOrderFields[key];
    }
  });

  if (Object.keys(mappedOrderFields).length > 0) {
    await knex("order_detail")
      .update(mappedOrderFields)
      .where("id", orderId)
      .where("user_id", userId);
  }

  if (order?.delivery_date) {
    await knex("order_status")
      .update({
        delivery_date: order.delivery_date
          ? dayjs.utc(order.delivery_date).format("YYYY-MM-DD")
          : null,
      })
      .where("order_id", orderId);
  }

  let itemsResult;
  if (orderItems && orderItems.length > 0) {
    // Get user's delivery fee
    const user = await knex("attain_user")
      .select("config")
      .where("id", userId)
      .first();

    const deliveryFee = user?.config?.delivery_fee || 0;

    const trxProvider = knex.transactionProvider();
    const trx = await trxProvider();

    const orderItemsToReplace = orderItems.map((item) => ({
      order_id: orderId,
      item_id: item.id,
      quantity: item.quantity,
      price_purchased_at: item.price_purchased_at,
      item_uom_id: item.itemUomId,
      notes: item.notes,
    }));

    // Add delivery fee if configured and not already included
    const hasDeliveryFee = orderItems.some((item) => item.id === null);
    if (deliveryFee > 0 && !hasDeliveryFee) {
      orderItemsToReplace.push({
        order_id: orderId,
        item_id: null, // Delivery fee has null item_id
        quantity: 1,
        price_purchased_at: deliveryFee,
        item_uom_id: null,
        notes: null,
      });
    }

    await trx("order_detail")
      .update({
        subtotal: orderItemsToReplace.reduce(
          (acc, item) => acc + item.price_purchased_at * item.quantity,
          0
        ),
      })
      .where("id", orderId);

    itemsResult = await trx("order_item")
      .delete()
      .where("order_id", orderId)
      .then(async () => {
        return await (
          await trxProvider()
        )("order_item").insert(orderItemsToReplace);
      })
      .then(async () => {
        (await trxProvider()).commit();
        return "success";
      })
      .catch(async (error) => {
        (await trxProvider()).rollback();
        const errorMessage = `Error updating order items for order id ${orderId}: ${error}`;
        console.error(
          `Error updating order items for order id: ${orderId}`,
          error
        );
        throw errorMessage;
      });
  }

  const invoiceFromOrder = await getInvoiceByOrderId(orderId);
  if (invoiceFromOrder) {
    await updateInvoiceFromOrder(supplierId, orderId, invoice);
  } else {
    await createInvoiceFromOrder(supplierId, orderId);
  }

  if (orderItems && orderItems.length > 0) {
    return itemsResult;
  }
  return "success";
};
