import knex from "../../../knex/knex";
import { CartItem, SubCart } from "../../generated/graphql";
import { populateCartItemsUOMs } from "../itemService/uomService";

const getCart = async (cartId) => {
  const cartItems: CartItem[] = await knex
    .select(
      "item.*",
      "cart_item.id as id",
      "cart_item.item_id",
      "cart_item.quantity",
      "cart_item.cart_id",
      "cart_item.item_uom_id",
      "cart_item.custom_price",
      "cart_item.min_sale_price"
    )
    .from("item")
    .join("cart_item", "item.id", "cart_item.item_id")
    .where("cart_id", cartId)
    .orderBy("item.supplier", "asc")
    .orderBy("cart_item.id", "desc");

  // Populate UOM data for cart items
  await populateCartItemsUOMs(cartItems);

  const user_id = await knex("cart")
    .select("user_id")
    .where("id", cartId)
    .first();

  const subCartsObject = cartItems.reduce(function (r, a) {
    r[a.supplier] = r[a.supplier] || [];
    r[a.supplier].push(a);
    return r;
  }, Object.create(null));

  const subCarts: SubCart[] = Object.entries(subCartsObject).map(
    ([key, value]) => {
      return {
        supplier: key,
        cartItems: value as CartItem[],
      };
    }
  );

  return {
    userId: user_id.user_id,
    id: cartId,
    cartItems: cartItems,
    subCarts: subCarts,
  };
};

export default getCart;
