import knex from "../../knex/knex";

/*
 * Investigation script to analyze drop_pack configuration values in attain_user table
 *
 * This script will:
 * 1. Find all users with config that contains "drop_pack" key
 * 2. Count occurrences of each drop_pack value
 * 3. Show detailed breakdown of the configuration
 *
 * Usage: npx ts-node scripts/investigations/dropPackConfigAnalysis.ts
 */

interface UserConfig {
  id: number;
  name: string;
  config: any;
  drop_pack?: string;
}

interface DropPackAnalysis {
  value: string;
  count: number;
  users: Array<{
    id: number;
    name: string;
    fullConfig: any;
  }>;
}

const analyzeDropPackConfig = async () => {
  try {
    console.log("🔍 Starting drop_pack configuration analysis...\n");

    // Get all users with config that contains drop_pack
    const usersWithDropPack = await knex("attain_user")
      .select("id", "name", "config")
      .whereNotNull("config")
      .whereRaw("config::text LIKE '%drop_pack%'");

    console.log(
      `📊 Found ${usersWithDropPack.length} users with drop_pack configuration\n`
    );

    if (usersWithDropPack.length === 0) {
      console.log("No users found with drop_pack configuration.");
      return;
    }

    // Parse configs and extract drop_pack values
    const parsedUsers: UserConfig[] = [];
    const parseErrors: Array<{ id: number; name: string; error: string }> = [];

    for (const user of usersWithDropPack) {
      try {
        const config =
          typeof user.config === "string"
            ? JSON.parse(user.config)
            : user.config;

        if (config && typeof config === "object" && "drop_pack" in config) {
          parsedUsers.push({
            id: user.id,
            name: user.name,
            config: config,
            drop_pack: config.drop_pack,
          });
        }
      } catch (error) {
        parseErrors.push({
          id: user.id,
          name: user.name,
          error: error.message,
        });
      }
    }

    console.log(
      `✅ Successfully parsed ${parsedUsers.length} user configurations`
    );
    if (parseErrors.length > 0) {
      console.log(`⚠️  Failed to parse ${parseErrors.length} configurations\n`);
    }

    // Analyze drop_pack values
    const dropPackCounts = new Map<string, DropPackAnalysis>();

    for (const user of parsedUsers) {
      const dropPackValue = user.drop_pack || "null/undefined";

      if (!dropPackCounts.has(dropPackValue)) {
        dropPackCounts.set(dropPackValue, {
          value: dropPackValue,
          count: 0,
          users: [],
        });
      }

      const analysis = dropPackCounts.get(dropPackValue)!;
      analysis.count++;
      analysis.users.push({
        id: user.id,
        name: user.name,
        fullConfig: user.config,
      });
    }

    // Display results
    console.log("📈 DROP_PACK VALUE ANALYSIS");
    console.log("=".repeat(50));

    // Sort by count (descending)
    const sortedAnalysis = Array.from(dropPackCounts.values()).sort(
      (a, b) => b.count - a.count
    );

    let totalUsers = 0;
    for (const analysis of sortedAnalysis) {
      totalUsers += analysis.count;
      console.log(`\n🏷️  Value: "${analysis.value}"`);
      console.log(`   Count: ${analysis.count} users`);
      console.log(
        `   Percentage: ${((analysis.count / parsedUsers.length) * 100).toFixed(
          1
        )}%`
      );
    }

    console.log(`\n📊 SUMMARY`);
    console.log(`Total users analyzed: ${totalUsers}`);
    console.log(`Unique drop_pack values: ${dropPackCounts.size}`);

    // Show detailed breakdown for each value
    console.log(`\n📋 DETAILED BREAKDOWN`);
    console.log("=".repeat(50));

    for (const analysis of sortedAnalysis) {
      console.log(
        `\n🔸 Drop Pack Value: "${analysis.value}" (${analysis.count} users)`
      );
      console.log("   Users:");

      // Show first 10 users for each value, or all if less than 10
      const usersToShow = analysis.users.slice(0, 10);
      for (const user of usersToShow) {
        console.log(`   - ID: ${user.id}, Name: ${user.name}`);
      }

      if (analysis.users.length > 10) {
        console.log(`   ... and ${analysis.users.length - 10} more users`);
      }

      // Show a sample configuration
      if (analysis.users.length > 0) {
        console.log(`\n   Sample configuration:`);
        console.log(
          `   ${JSON.stringify(analysis.users[0].fullConfig, null, 2)}`
        );
      }
    }

    // Show parse errors if any
    if (parseErrors.length > 0) {
      console.log(`\n❌ PARSE ERRORS`);
      console.log("=".repeat(50));
      for (const error of parseErrors) {
        console.log(`User ID ${error.id} (${error.name}): ${error.error}`);
      }
    }

    // Export detailed data to JSON file for further analysis
    const exportData = {
      summary: {
        totalUsersAnalyzed: totalUsers,
        uniqueDropPackValues: dropPackCounts.size,
        analysisDate: new Date().toISOString(),
      },
      valueBreakdown: sortedAnalysis,
      parseErrors: parseErrors,
    };

    const fs = require("fs");
    const path = require("path");
    const exportPath = path.join(__dirname, "drop_pack_analysis_results.json");

    fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2));
    console.log(`\n💾 Detailed results exported to: ${exportPath}`);
  } catch (error) {
    console.error("❌ Error during analysis:", error);
    throw error;
  } finally {
    // Close database connection
    await knex.destroy();
    console.log("\n🔚 Database connection closed");
  }
};

// Run the analysis
if (require.main === module) {
  analyzeDropPackConfig()
    .then(() => {
      console.log(
        "\n✅ Drop pack configuration analysis completed successfully"
      );
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Analysis failed:", error);
      process.exit(1);
    });
}

export { analyzeDropPackConfig };
