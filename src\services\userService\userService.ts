import { Knex } from "knex";
import knex from "../../../knex/knex";
import {
  CustomPrice,
  CustomPriceWithoutUserId,
  Ordering,
  User,
  UserInput,
} from "../../generated/graphql";
import { getRoutesOnDay } from "../routeService/routeService";
import getUsersSuppliers from "./getUsersSuppliers";
import {
  updateUserCustomUOMPrices,
  getGroupCustomUOMPrices,
  updateGroupCustomUOMPrices,
} from "../itemService/uomService";
import {
  updateCustomerHiddenProducts,
  updateGroupHiddenProducts,
  getGroupHiddenProducts,
} from "../itemService/customerHiddenProductsService";
import dayjs from "dayjs";

// GETs

export const getUser = async (userId: string): Promise<User | null> => {
  const result: User = await knex("attain_user").where("id", userId).first();
  return result;
};

export const getUsers = async ({
  supplierId,
  filters,
  pagination,
  sortBy,
  includeCustomPrices = false,
  includeSuppliers = false,
}: {
  supplierId?: string;
  filters?: {
    ids?: string[];
    routeIds?: string[];
    searchTerm?: string;
    drivers?: string[];
    name?: string;
    approved?: boolean;
    active?: boolean;
    storeGroup?: string;
    archived?: boolean;
    includeUnassignedRoute?: boolean;
    orderDate?: Date;
    deliveryDate?: Date;
  };
  pagination?: { offset?: number; limit?: number };
  sortBy?: { field: string; ordering: Ordering };
  includeCustomPrices?: boolean;
  includeSuppliers?: boolean;
} = {}): Promise<{ users: User[]; totalCount: number }> => {
  if (!supplierId) {
    // TODO: we should throw if we don't have a supplierID, but rn the app only has
    // the userID after logging in, so we cant't do this yet. Ref todo below.
    // console.error("Supplier ID is required for getUsers");
    // throw new Error("Supplier ID is required for getUsers");
  }

  const filtersWithDefaults = {
    archived: false,
    ...filters,
  };

  const { offset, limit } = pagination ?? { offset: 0, limit: 100000 };
  const { field: sortField, ordering: sortOrdering } = sortBy ?? {
    field: "name",
    ordering: Ordering.Asc,
  };

  // TODO: ref todo above
  const query = supplierId
    ? knex({ u: "attain_user" })
        .select("u.*")
        .innerJoin({ st: "supplier_times" }, "u.id", "st.business_id")
        .where("st.supplier_id", supplierId)
        .where("u.supplier_beta", true)
        .where("u.archived", filtersWithDefaults.archived)
    : knex({ u: "attain_user" })
        .select("u.*")
        .whereIn("id", filtersWithDefaults.ids);
  // Filters
  if (filtersWithDefaults.ids && filtersWithDefaults.ids.length > 0) {
    query.whereIn("u.id", filtersWithDefaults.ids);
  }
  if (filters?.searchTerm && filtersWithDefaults.searchTerm.length > 0) {
    query.where((builder) => {
      builder
        .where("u.name", "ilike", `%${filtersWithDefaults.searchTerm}%`)
        .orWhere("u.address", "ilike", `%${filtersWithDefaults.searchTerm}%`)
        .orWhere(
          "u.store_group",
          "ilike",
          `%${filtersWithDefaults.searchTerm}%`
        )
        .orWhere("u.owner_name", "ilike", `%${filtersWithDefaults.searchTerm}%`);
    });
  }
  if (filters?.active !== null && filters?.active !== undefined) {
    query.whereRaw(
      `
      CASE
        WHEN u.config IS NULL THEN false
        ELSE (u.config->>'active')::boolean = ?
      END
    `,
      filtersWithDefaults.active
    );
  }
  if (filters?.name) {
    query.where("u.name", "ilike", `%${filtersWithDefaults.name}%`);
  }
  if (filters?.approved !== null && filters?.approved !== undefined) {
    query.where("u.approved", filtersWithDefaults.approved);
  }
  if (filters?.storeGroup) {
    query.where("u.store_group", filtersWithDefaults.storeGroup);
  }
  if (
    filters?.drivers &&
    filters?.drivers.length > 0 &&
    !filtersWithDefaults.drivers.includes("all_drivers_default")
  ) {
    const routeIds = await knex("route")
      .select("id")
      .where("supplier_id", supplierId)
      .whereIn("driver", filtersWithDefaults.drivers);

    query.whereIn(
      "u.route_id",
      routeIds.map((route) => route.id)
    );
  }

  if (filters?.routeIds && filtersWithDefaults.routeIds.length > 0) {
    if (filters?.includeUnassignedRoute) {
      query.whereNull("u.route_id");
    } else {
      query.whereIn("u.route_id", filtersWithDefaults.routeIds);
    }
  }
  if (filters?.orderDate) {
    query.whereExists((builder) => {
      builder
        .select(knex.raw(1))
        .from("order_detail")
        .whereRaw("order_detail.user_id = u.id")
        .whereRaw("DATE(order_detail.date_submitted) = ?", [
          dayjs(filters.orderDate).format("YYYY-MM-DD"),
        ]);
    });
  }

  if (filters?.deliveryDate) {
    query.whereExists((builder) => {
      builder
        .select(knex.raw(1))
        .from("order_detail")
        .innerJoin("order_status", "order_detail.id", "order_status.order_id")
        .whereRaw("order_detail.user_id = u.id")
        .whereRaw("DATE(order_status.delivery_date) = ?", [
          dayjs(filters.deliveryDate).format("YYYY-MM-DD"),
        ]);
    });
  }

  // Get total count
  const countQuery = query
    .clone()
    .clearSelect()
    .clearOrder()
    .count("u.id as count")
    .first();
  const totalCount = parseInt((await countQuery).count as string, 10);

  // Sort fields
  const allowedSortFields = [
    "id",
    "name",
    "address",
    "store_group",
    "route_id",
    "created_at",
    "updated_at",
    "last_order_date",
  ];
  if (allowedSortFields.includes(sortField)) {
    query.orderBy(`u.${sortField}`, sortOrdering);
  } else {
    query.orderBy("u.id", Ordering.Asc);
  }

  // Apply pagination
  query.offset(offset).limit(limit);
  const users = await query;

  if (includeSuppliers) {
    await getUsersSuppliers(users);
  }
  if (includeCustomPrices) {
    await populateUsersCustomPrices(users);
  }
  return { users, totalCount };
};

export const populateUsersCustomPrices = async (users: User[]) => {
  const userIds = users.map((user) => user.id);

  const customPrices = await knex("custom_item_price")
    .select("user_id", "item_id", "price")
    .whereIn("user_id", userIds);

  const userCustomPrices = new Map();
  customPrices.forEach((price) => {
    if (!userCustomPrices.has(price.user_id)) {
      userCustomPrices.set(price.user_id, []);
    }
    userCustomPrices.get(price.user_id).push(price);
  });

  users.forEach((user) => {
    user.custom_prices = userCustomPrices.get(user.id) || [];
  });

  return users;
};

export const populateDeliveryWindows = async (users: User[]) => {
  const userIds = users.map((user) => user.id);

  const deliveryWindows = await knex("delivery_window")
    .select("*")
    .whereIn("store_id", userIds);

  // Group delivery windows by store_id
  const deliveryWindowMap = new Map();
  deliveryWindows.forEach((window) => {
    const storeId = window.store_id;
    if (!deliveryWindowMap.has(storeId)) {
      deliveryWindowMap.set(storeId, {
        id: window.id,
        days_of_week: [],
      });
    }
    deliveryWindowMap.get(storeId).days_of_week.push(window.day);
  });

  // Add delivery_window to each user
  users.forEach((user) => {
    const deliveryWindowData = deliveryWindowMap.get(user.id);
    if (deliveryWindowData) {
      user.delivery_window = {
        days_of_week: deliveryWindowData.days_of_week,
        start_time: (user as any).start_time,
        end_time: (user as any).end_time,
      };
    } else {
      user.delivery_window = null;
    }
  });
};

export const getUserCustomPrices = async (
  user: User
): Promise<CustomPriceWithoutUserId[] | null> => {
  const customPrices = await knex("custom_item_price")
    .select("item_id", "price")
    .where("user_id", user.id);

  return customPrices;
};

export const getGroupCustomPrices = async (
  groupname: string
): Promise<CustomPriceWithoutUserId[] | null> => {
  const user = await knex("attain_user")
    .where({ store_group: groupname })
    .first();

  if (!user) {
    throw new Error(`Group does not exist: ${groupname}`);
  }

  return getUserCustomPrices(user);
};

export const getGroups = async (supplierId: string): Promise<string[]> => {
  const result: { store_group: string }[] = await knex("attain_user as a")
    .leftJoin("supplier_times as st", "a.id", "st.business_id")
    .whereNotNull("a.store_group")
    .where("st.supplier_id", supplierId)
    .distinct("a.store_group");
  return result.map((group) => group.store_group);
};

export const getUsersScheduledOnDay = async (
  supplierId: string,
  date: Date
): Promise<User[]> => {
  const routes = await getRoutesOnDay(supplierId, date);
  if (routes.length === 0) {
    return [];
  }
  const users = await getUsers({
    supplierId,
    filters: { routeIds: routes.map((route) => route.id.toString()) },
  });
  return users.users;
};

export const upsertDeliveryWindow = async (
  txn: Knex.Transaction,
  userId: string,
  delivery_window?: {
    days_of_week?: string[];
    start_time?: string;
    end_time?: string;
  }
) => {
  // Delete existing delivery windows for this user
  await txn("delivery_window").where("store_id", userId).delete();

  // Update start_time and end_time in attain_user table
  if (delivery_window) {
    await txn("attain_user")
      .where("id", userId)
      .update({
        start_time: delivery_window.start_time || null,
        end_time: delivery_window.end_time || null,
      });
  }

  // If delivery_window is provided and has valid days, insert new records
  if (
    delivery_window &&
    delivery_window.days_of_week &&
    delivery_window.days_of_week.length > 0
  ) {
    const { days_of_week } = delivery_window;

    const deliveryWindowRecords = days_of_week.map((day) => ({
      store_id: parseInt(userId),
      day,
    }));

    await txn("delivery_window").insert(deliveryWindowRecords);
  }
};

export const updateUserCustomPrices = async (
  txn: Knex.Transaction,
  userId: string,
  custom_prices: CustomPriceWithoutUserId[]
) => {
  await txn("custom_item_price").where("user_id", userId).delete();
  if (custom_prices.length > 0) {
    await txn("custom_item_price")
      .insert(custom_prices.map((cp) => ({ ...cp, user_id: userId })))
      .onConflict(["user_id", "item_id"])
      .merge()
      .returning(["item_id", "price"]);
  }
};

export const updateAllUserCustomItemPrice = async (
  supplierId: string,
  item_id: string,
  price: number,
  overrideExistingPrices = false
) => {
  const users = await getUsers({ supplierId });
  const userIds = users.users.map((u) => u.id);
  const custom_price_data = userIds.map((userId) => ({
    user_id: userId,
    item_id,
    price,
  }));

  const txn = await knex.transaction();
  try {
    if (overrideExistingPrices) {
      await txn("custom_item_price")
        .insert(custom_price_data)
        .onConflict(["user_id", "item_id"])
        .merge();
    } else {
      await txn("custom_item_price")
        .insert(custom_price_data)
        .onConflict(["user_id", "item_id"])
        .ignore();
    }
    await txn.commit();
  } catch (e) {
    await txn.rollback();
    throw e;
  }
};

export const getUsersInGroup = async (
  groupname: string
): Promise<User[] | null> => {
  const result: User[] = await knex("attain_user").where({
    store_group: groupname,
  });
  return result;
};

export const updateGroupCustomPrices = async (
  txn: Knex.Transaction,
  store_group: string,
  custom_prices: CustomPriceWithoutUserId[]
) => {
  const usersInGroup = await getUsersInGroup(store_group);
  const result = await Promise.all(
    usersInGroup.map(async (user) => {
      await updateUserCustomPrices(txn, user.id, custom_prices);
    })
  );
  return result;
};

export const checkIfStoreIsInGroup = async (
  userId: string,
  groupName: string,
  supplierId: string
) => {
  const usersInGroup = await getGroups(supplierId);
  return usersInGroup.includes(groupName);
};

export const updateUser = async (txn: Knex.Transaction, user: UserInput) => {
  const {
    id: userId,
    custom_prices,
    delivery_window,
    hidden_products,
    custom_uom_prices,
    net_terms_days,
    ...userData
  } = user;

  // Clean up user addresses
  if (user.address) {
    user.address = user.address.replaceAll(/(,(?=\S))/g, ", ");
  }

  const currentUser: User = await getUser(userId);
  if (!currentUser) {
    throw new Error(`User not found with ID: ${userId}`);
  }

  const supplierId = await txn("supplier_times")
    .select("supplier_id")
    .where("business_id", userId)
    .first();

  const existingGroups = await checkIfStoreIsInGroup(
    userId,
    user.store_group,
    supplierId.supplier_id
  );
  const isExistingGroup = user.store_group ? existingGroups : false;

  // Case 1: User is being moved to a different group
  if (currentUser.store_group !== user.store_group) {
    if (isExistingGroup) {
      // Apply group prices (both regular and UOM)
      const groupPrices = await getGroupCustomPrices(user.store_group);
      await updateUserCustomPrices(txn, userId, groupPrices);

      const groupUOMPrices = await getGroupCustomUOMPrices(
        user.store_group,
        supplierId.supplier_id
      );
      if (groupUOMPrices && groupUOMPrices.length > 0) {
        await updateUserCustomUOMPrices(txn, userId, groupUOMPrices);
      }
    } else {
      // Apply individual prices (both regular and UOM)
      await updateUserCustomPrices(txn, userId, custom_prices || []);

      if (custom_uom_prices !== undefined) {
        await updateUserCustomUOMPrices(txn, userId, custom_uom_prices || []);
      }
    }
  }
  // Case 2: User is not in a group and prices are being updated
  else if (
    !user.store_group &&
    (custom_prices !== undefined || custom_uom_prices !== undefined)
  ) {
    // Apply individual prices (both regular and UOM)
    if (custom_prices !== undefined) {
      await updateUserCustomPrices(txn, userId, custom_prices || []);
    }

    if (custom_uom_prices !== undefined) {
      await updateUserCustomUOMPrices(txn, userId, custom_uom_prices || []);
    }
  }
  // Case 3: User is in a group and prices are being updated
  else if (
    user.store_group &&
    (custom_prices !== undefined || custom_uom_prices !== undefined)
  ) {
    // Apply group prices (both regular and UOM)
    if (custom_prices !== undefined) {
      await updateGroupCustomPrices(txn, user.store_group, custom_prices || []);
    }

    if (custom_uom_prices !== undefined) {
      await updateGroupCustomUOMPrices(
        txn,
        user.store_group,
        custom_uom_prices || []
      );
    }
  }

  // Handle hidden products updates
  // Case 1: User is being moved to a different group
  if (currentUser.store_group !== user.store_group) {
    if (isExistingGroup) {
      const groupHiddenProducts = await getGroupHiddenProducts(
        supplierId.supplier_id,
        user.store_group
      );
      await updateCustomerHiddenProducts(
        txn,
        supplierId.supplier_id,
        userId,
        groupHiddenProducts
      );
    } else {
      await updateCustomerHiddenProducts(
        txn,
        supplierId.supplier_id,
        userId,
        hidden_products || []
      );
    }
  }
  // Case 2: User is not in a group and hidden products are being updated
  else if (!user.store_group && hidden_products !== undefined) {
    await updateCustomerHiddenProducts(
      txn,
      supplierId.supplier_id,
      userId,
      hidden_products || []
    );
  }
  // Case 3: User is in a group and hidden products are being updated
  else if (user.store_group && hidden_products !== undefined) {
    await updateGroupHiddenProducts(
      txn,
      supplierId.supplier_id,
      user.store_group,
      hidden_products || []
    );
  }

  // Update delivery window if provided
  if (delivery_window !== undefined) {
    await upsertDeliveryWindow(txn, userId, delivery_window);
  }

  // Update net_terms_days only if provided
  if (net_terms_days !== undefined) {
    if (isExistingGroup) {
      await txn("attain_user")
        .update({ net_terms_days: net_terms_days })
        .where({ store_group: currentUser.store_group });
    } else {
      await txn("attain_user")
        .update({ net_terms_days: net_terms_days })
        .where({ id: userId });
    }
  }

  // Update user data
  const updatedUser: User = (
    await txn("attain_user").update(userData).where("id", userId).returning("*")
  )[0];

  // Fetch updated custom prices
  updatedUser.custom_prices = await getUserCustomPrices(updatedUser);

  return updatedUser;
};
