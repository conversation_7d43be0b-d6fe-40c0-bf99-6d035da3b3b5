// src/graphql/queries/employeeWithDetails.ts
import { getEmployeeWithDetails } from "../../services/accessService/getEmployees";

const employeeWithDetailsResolver = async (_, { employeeId, supplierId }) => {
  try {
    return await getEmployeeWithDetails(employeeId, supplierId);
  } catch (error) {
    console.error("Error in employeeWithDetails query:", error);
    throw new Error("Failed to fetch employee details");
  }
};

export default employeeWithDetailsResolver;
